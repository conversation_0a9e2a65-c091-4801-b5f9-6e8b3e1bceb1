# Order Status Flow - Manual Testing Guide

This guide provides step-by-step instructions for manually testing the complete order status flow across all user interfaces.

## Prerequisites

1. **Test Environment Setup**
   - Server running on `http://localhost:5000`
   - Client running on `http://localhost:3000`
   - Database with test data (orders, vendors, customers)
   - Socket.io connection enabled

2. **Test Accounts Required**
   - Admin account
   - Vendor account  
   - Customer account with existing orders

## Test Scenarios

### Scenario 1: Admin Updates Order Status

**Objective**: Verify that admin order status changes sync to vendor panel and customer tracking

**Steps**:
1. **Login as Admin**
   - Navigate to `/admin/login`
   - Login with admin credentials
   - Go to Orders Management section

2. **Verify Initial State**
   - Check that orders are displayed with current status
   - Note the statistics cards (Total, Pending, Delivered, Earnings)
   - Take screenshot for comparison

3. **Update Order Status**
   - Find an order with status "pending"
   - Click on the status dropdown
   - Change status to "processing"
   - Verify success notification appears
   - Check that statistics cards update immediately

4. **Verify Vendor Panel Sync**
   - Open new browser tab/window
   - Login as vendor (for the same order)
   - Navigate to vendor orders management
   - **Expected**: Order status should show "processing"
   - **Expected**: Real-time notification should appear about admin update

5. **Verify Customer Tracking**
   - Open another browser tab
   - Navigate to order tracking page
   - Enter the order's tracking number
   - **Expected**: Status should show "processing"
   - **Expected**: Timeline should include the status update

**Pass Criteria**:
- ✅ Admin can successfully update order status
- ✅ Vendor panel reflects the change immediately
- ✅ Customer tracking shows updated status
- ✅ Statistics update correctly
- ✅ Real-time notifications work

---

### Scenario 2: Vendor Updates Order Status

**Objective**: Verify that vendor order status changes sync to admin panel and customer tracking

**Steps**:
1. **Login as Vendor**
   - Navigate to `/vendor/login`
   - Login with vendor credentials
   - Go to Orders section

2. **Update Order Status**
   - Find an order with status "processing"
   - Change status to "shipped"
   - Add tracking information if prompted
   - Verify success notification

3. **Verify Admin Panel Sync**
   - Switch to admin panel tab
   - **Expected**: Order status should update to "shipped"
   - **Expected**: Real-time notification about vendor update
   - **Expected**: Statistics should update

4. **Verify Customer Tracking**
   - Switch to customer tracking tab
   - Click refresh button or reload page
   - **Expected**: Status shows "shipped"
   - **Expected**: Timeline includes shipping update

**Pass Criteria**:
- ✅ Vendor can successfully update order status
- ✅ Admin panel reflects the change immediately
- ✅ Customer tracking shows updated status
- ✅ Real-time notifications work in both directions

---

### Scenario 3: Real-time Synchronization Test

**Objective**: Test simultaneous users and real-time updates

**Steps**:
1. **Setup Multiple Browser Sessions**
   - Browser 1: Admin panel (orders management)
   - Browser 2: Vendor panel (orders management)
   - Browser 3: Customer tracking page

2. **Test Concurrent Updates**
   - In admin panel: Update order status to "delivered"
   - **Expected**: Vendor panel should update immediately
   - **Expected**: Customer tracking should reflect change
   - **Expected**: All panels show consistent data

3. **Test Statistics Consistency**
   - Check statistics cards in both admin and vendor panels
   - **Expected**: Numbers should be consistent
   - **Expected**: Updates should be immediate

**Pass Criteria**:
- ✅ All interfaces update in real-time
- ✅ No data inconsistencies
- ✅ Statistics remain accurate across panels

---

### Scenario 4: Customer Order Tracking Features

**Objective**: Test customer-facing order tracking functionality

**Steps**:
1. **Access Order Tracking**
   - Navigate to `/track-order`
   - Test both tracking number and order ID search

2. **Test Refresh Functionality**
   - Search for an order
   - Click the refresh button
   - **Expected**: Success notification appears
   - **Expected**: Data refreshes from server

3. **Test Real-time Updates**
   - Keep tracking page open
   - In another tab, update order status as admin/vendor
   - **Expected**: Tracking page should reflect changes
   - **Expected**: Timeline should update

**Pass Criteria**:
- ✅ Tracking search works correctly
- ✅ Refresh functionality works
- ✅ Real-time updates appear on tracking page

---

### Scenario 5: Error Handling and Edge Cases

**Objective**: Test system behavior under error conditions

**Steps**:
1. **Network Error Simulation**
   - Disconnect internet briefly
   - Try to update order status
   - **Expected**: Error notification appears
   - **Expected**: System recovers when connection restored

2. **Invalid Status Transitions**
   - Try updating delivered order back to pending
   - **Expected**: Appropriate validation/error handling

3. **Socket Connection Issues**
   - Disable WebSocket in browser dev tools
   - Update order status
   - **Expected**: Updates still work via API calls
   - **Expected**: Graceful fallback behavior

**Pass Criteria**:
- ✅ Error messages are user-friendly
- ✅ System recovers from network issues
- ✅ Graceful degradation when sockets fail

---

## Performance Testing

### Load Testing
1. **Multiple Simultaneous Updates**
   - Have 3-5 users update different orders simultaneously
   - **Expected**: All updates process correctly
   - **Expected**: No data corruption or race conditions

2. **Large Order Lists**
   - Test with 100+ orders in the system
   - **Expected**: Pagination works correctly
   - **Expected**: Status updates remain fast

---

## Test Data Requirements

### Sample Orders Needed
```json
{
  "orders": [
    {
      "id": "order-1",
      "orderNumber": "ORD-2024-001",
      "status": "pending",
      "customer": "customer-1",
      "vendor": "vendor-1",
      "items": [...],
      "totalAmount": 100
    },
    {
      "id": "order-2", 
      "orderNumber": "ORD-2024-002",
      "status": "processing",
      "customer": "customer-2",
      "vendor": "vendor-1",
      "items": [...],
      "totalAmount": 200
    }
  ]
}
```

---

## Checklist for Complete Testing

### Functional Tests
- [ ] Admin can update order status
- [ ] Vendor can update order status  
- [ ] Customer can track orders
- [ ] Real-time sync works admin ↔ vendor
- [ ] Real-time sync works admin/vendor → customer
- [ ] Statistics update correctly
- [ ] Notifications appear for external updates

### UI/UX Tests
- [ ] Status dropdowns work correctly
- [ ] Loading states display properly
- [ ] Success/error notifications appear
- [ ] Responsive design works on mobile
- [ ] Cards and statistics display correctly

### Technical Tests
- [ ] Socket connections establish correctly
- [ ] API calls succeed
- [ ] Error handling works
- [ ] Performance is acceptable
- [ ] No console errors
- [ ] Memory leaks don't occur

### Cross-browser Tests
- [ ] Chrome
- [ ] Firefox  
- [ ] Safari
- [ ] Edge

---

## Reporting Issues

When reporting issues, include:
1. **Steps to reproduce**
2. **Expected vs actual behavior**
3. **Browser and version**
4. **Console errors (if any)**
5. **Screenshots/videos**
6. **Network tab information**

---

## Success Criteria

The order status flow is considered fully functional when:
- ✅ All status updates sync in real-time across interfaces
- ✅ Customer tracking reflects admin/vendor changes immediately  
- ✅ Statistics remain accurate and consistent
- ✅ Error handling is robust and user-friendly
- ✅ Performance is acceptable under normal load
- ✅ UI is responsive and intuitive
