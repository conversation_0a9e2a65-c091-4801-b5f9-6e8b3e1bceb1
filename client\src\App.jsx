import React from "react";
import { Routes, Route, useLocation } from "react-router-dom";
import { CartProvider } from "./contexts/CartContext";
import { SearchProvider } from "./contexts/SearchContext";
import { CurrencyProvider } from "./contexts/CurrencyContext";
import Navbar from "./components/Navbar";
import LandingPage from "./pages/LandingPage";
import HomePage from "./pages/HomePage";
import ProductsPage from "./pages/ProductsPage";
import ProductDetailPage from "./pages/ProductDetailPage";
import OrdersPage from "./pages/OrdersPage";
import CartPage from "./pages/CartPage";
import CheckoutPage from "./pages/CheckoutPage";
import AuthPage from "./pages/AuthPage";
import ProfilePage from "./pages/ProfilePage";
import SettingsPage from "./pages/SettingsPage";
import DebugPage from "./pages/DebugPage";
import OrderTrackingPage from "./pages/OrderTrackingPage";
import SignUp from "./components/SignUp";
import EmailVerificationForm from "./components/auth/EmailVerificationForm";
import ResetPasswordForm from "./components/auth/ResetPasswordForm";
import ForgotPasswordForm from "./components/auth/ForgotPasswordForm";
import TestPanelsPage from "./pages/TestPanelsPage";
import SearchPage from "./pages/SearchPage";
import ProtectedRoute from "./components/ProtectedRoute";

// Admin Components
import AdminDashboardPage from "./pages/admin/AdminDashboardPage";
import AdminAuthPage from "./pages/admin/AdminAuthPage";

// Vendor Components
import VendorDashboardPage from "./pages/vendor/VendorDashboardPage";

function AppContent() {
  const location = useLocation();
  
  // Pages where we don't want to show the navbar
  const hideNavbarPaths = [
    '/',
    '/auth',
    '/login', 
    '/signup',
    '/verify-email',
    '/reset-password',
    '/forgot-password'
  ];
  
  // Check if current path starts with admin or vendor routes
  const isAdminOrVendorPage = location.pathname.startsWith('/admin') || location.pathname.startsWith('/vendor');
  
  const shouldShowNavbar = !hideNavbarPaths.includes(location.pathname) && !isAdminOrVendorPage;

  return (
    <>
      {shouldShowNavbar && <Navbar />}
      <Routes>
        {/* Landing Page - Public Route */}
        <Route path="/" element={<LandingPage />} />
        
        {/* Protected Home Route - Requires Authentication */}
        <Route path="/home" element={
          <ProtectedRoute>
            <HomePage />
          </ProtectedRoute>
        } />
        
        {/* Protected Routes - Require Authentication */}
        <Route path="/page" element={
          <ProtectedRoute>
            <ProductsPage />
          </ProtectedRoute>
        } />
        <Route path="/product/:id" element={
          <ProtectedRoute>
            <ProductDetailPage />
          </ProtectedRoute>
        } />
        <Route path="/orders" element={
          <ProtectedRoute>
            <OrdersPage />
          </ProtectedRoute>
        } />
        <Route path="/cart" element={
          <ProtectedRoute>
            <CartPage />
          </ProtectedRoute>
        } />
        <Route path="/checkout" element={
          <ProtectedRoute>
            <CheckoutPage />
          </ProtectedRoute>
        } />
        <Route path="/track-order" element={
          <ProtectedRoute>
            <OrderTrackingPage />
          </ProtectedRoute>
        } />
        <Route path="/track-order/:trackingNumber" element={
          <ProtectedRoute>
            <OrderTrackingPage />
          </ProtectedRoute>
        } />
        <Route path="/search" element={
          <ProtectedRoute>
            <SearchPage />
          </ProtectedRoute>
        } />
        <Route path="/profile" element={
          <ProtectedRoute>
            <ProfilePage />
          </ProtectedRoute>
        } />
        <Route path="/settings" element={
          <ProtectedRoute>
            <SettingsPage />
          </ProtectedRoute>
        } />
        <Route path="/debug" element={
          <ProtectedRoute>
            <DebugPage />
          </ProtectedRoute>
        } />
        <Route path="/test-panels" element={
          <ProtectedRoute>
            <TestPanelsPage />
          </ProtectedRoute>
        } />
        
        {/* Public Authentication Routes */}
        <Route path="/auth" element={<AuthPage />} />
        <Route path="/login" element={<AuthPage />} />
        <Route path="/signup" element={<SignUp />} />
        
        {/* Authentication Routes */}
        <Route path="/verify-email" element={<EmailVerificationForm />} />
        <Route path="/reset-password" element={<ResetPasswordForm />} />
        <Route path="/forgot-password" element={<ForgotPasswordForm onBackToLogin={() => window.location.href = '/auth'} />} />
        
        {/* Admin Routes */}
        <Route path="/admin/auth" element={<AdminAuthPage />} />
        <Route path="/admin" element={<AdminDashboardPage />} />
        <Route path="/admin/dashboard" element={<AdminDashboardPage />} />

        {/* Vendor Routes */}
        <Route path="/vendor" element={<VendorDashboardPage />} />
        <Route path="/vendor/dashboard" element={<VendorDashboardPage />} />
        
        {/* 404 Route */}
        <Route path="*" element={
          <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <h1 className="text-6xl font-bold text-gray-900">404</h1>
              <p className="text-xl text-gray-600 mt-4">Page Not Found</p>
              <a href="/" className="text-orange-500 hover:text-orange-600 mt-4 inline-block">
                Go back home
              </a>
            </div>
          </div>
        } />
      </Routes>
    </>
  );
}

function App() {
  return (
    <CurrencyProvider>
      <CartProvider>
        <SearchProvider>
          <AppContent />
        </SearchProvider>
      </CartProvider>
    </CurrencyProvider>
  );
}

export default App