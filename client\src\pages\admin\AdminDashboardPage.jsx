import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Spin } from 'antd';
import { useAuth } from '../../hooks/useAuth';
import AdminPanel from '../../components/admin/AdminPanel';

const AdminDashboardPage = () => {
  const navigate = useNavigate();
  const { isAuthenticated, userType, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        // Not authenticated at all, redirect to admin auth
        navigate('/admin/auth');
      } else if (userType !== 'admin') {
        // Authenticated but not as admin, redirect appropriately
        if (userType === 'vendor') {
          navigate('/vendor/dashboard');
        } else {
          navigate('/');
        }
      }
    }
  }, [isAuthenticated, userType, isLoading, navigate]);

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  // Only render admin panel if authenticated as admin
  if (isAuthenticated && userType === 'admin') {
    return <AdminPanel />;
  }

  // Return null while redirecting
  return null;
};

export default AdminDashboardPage;
