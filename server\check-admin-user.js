const mongoose = require('mongoose');
const dotenv = require('dotenv');

dotenv.config();

// Import User model
const User = require('./src/models/User');

async function checkAdminUser() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('✅ Connected to MongoDB');

    // Check if admin user exists
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    
    if (adminUser) {
      console.log('✅ Admin user found!');
      console.log('User details:');
      console.log('  Email:', adminUser.email);
      console.log('  User Type:', adminUser.userType);
      console.log('  First Name:', adminUser.firstName);
      console.log('  Last Name:', adminUser.lastName);
      console.log('  Status:', adminUser.status);
      console.log('  Email Verified:', adminUser.emailVerified);
      console.log('  Account Locked:', adminUser.accountLocked);
      console.log('  Created:', adminUser.createdAt);
    } else {
      console.log('❌ Admin user not found!');
      console.log('Creating admin user...');
      
      // Create admin user
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('password@admin123', 12);
      
      const newAdmin = new User({
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Admin',
        lastName: 'User',
        userType: 'admin',
        status: 'active',
        emailVerified: true,
        accountLocked: false
      });
      
      await newAdmin.save();
      console.log('✅ Admin user created successfully!');
    }

    // Also check all users
    const allUsers = await User.find({}).select('email userType status');
    console.log('\n📊 All users in database:');
    allUsers.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.email} (${user.userType}) - ${user.status}`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

checkAdminUser();
