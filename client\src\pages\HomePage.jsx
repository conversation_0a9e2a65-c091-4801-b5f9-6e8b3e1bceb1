import React, { useState, useEffect } from "react";
import CatergoriesMenu from "../components/ui/CaregoriesMenu";
import AutoScrollCarousel from "../components/ui/AutoScrollCarousel";
import ItemsCard from "../components/ItemsCard";
import ProductCards from "../components/ProductCards";
import AllProducts from "../components/AllProducts";
import Footer from "../components/Footer";
import Header from "../components/Header";
import { Card, Spin } from "antd";
import axios from "axios";
import { getApiUrl } from "../config/api";

const HomePage = () => {
  const [promotionImages, setPromotionImages] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchHomepageData();
  }, []);

  const fetchHomepageData = async () => {
    try {
      setLoading(true);
      const response = await axios.get(getApiUrl('public/homepage/data'));
      if (response.data.success) {
        // Get sidebar promotions
        const sidebarPromotions = response.data.data.promotions.sidebar || [];
        setPromotionImages(sidebarPromotions);
      }
    } catch (error) {
      console.error('Error fetching homepage data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePromotionClick = (linkUrl) => {
    if (linkUrl) {
      window.open(linkUrl, '_blank');
    }
  };

  return (
    <>
<Header />
      <div className="flex flex-col m-4 md:m-6 lg:m-8 bg-white md:flex-col lg:flex-row items-start justify-center gap-2 sm:gap-3 md:gap-4 w-full max-w-7xl mx-auto px-2 sm:px-4 md:px-6 py-2 sm:py-4">
        {/* Categories Menu - Hidden on small screens, visible on larger screens */}
        <div className="hidden lg:block w-full md:w-auto lg:w-auto lg:flex-shrink-0 order-1 lg:order-1 max-w-full">
          <div className="h-44 sm:h-52 md:h-60 lg:h-64 xl:h-72">
            <CatergoriesMenu />
          </div>
        </div>

        {/* Carousel - Center component */}
        <div className="w-full md:w-full lg:flex-1 order-2 lg:order-2 max-w-full overflow-hidden">
          <AutoScrollCarousel />
        </div>

        {/* Promotion - Hidden on small screens, visible only on larger screens */}
        <div className="hidden lg:block w-auto lg:flex-shrink-0 order-3 lg:order-3 max-w-full">
          <div className="h-44 sm:h-52 md:h-60 lg:h-64 xl:h-72">
            <Card className="w-full lg:w-72 xl:w-80 max-w-full border-none shadow-none bg-transparent h-full flex items-center justify-center">
              {loading ? (
                <div className="w-full h-full flex items-center justify-center">
                  <Spin size="large" />
                </div>
              ) : promotionImages.length > 0 ? (
                <img
                  src={promotionImages[0].imageUrl}
                  alt={promotionImages[0].title || "Featured promotion"}
                  className="w-full h-full object-cover rounded cursor-pointer"
                  onClick={() => handlePromotionClick(promotionImages[0].linkUrl)}
                  onError={(e) => {
                    e.target.src = 'https://via.placeholder.com/300x400?text=Image+Not+Found';
                  }}
                />
              ) : (
                <img
                  src="https://via.placeholder.com/300x400?text=No+Promotion+Available"
                  alt="No promotion available"
                  className="w-full h-full object-cover rounded"
                />
              )}
            </Card>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 py-6">
        <ItemsCard />
      </div>
      <div>
        <ProductCards />
      </div>
      <div>
        <AllProducts />
      </div>
      <div>
        <Footer />
      </div>
    </>
  );
};

export default HomePage;