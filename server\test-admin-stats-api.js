const express = require('express');
const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import Product model
const Product = require('./src/models/Product');
const { transformProductPricing } = require('./src/utils/currencyUtils');

// Connect to database
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce')
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => console.error('MongoDB connection error:', err));

// Simulate the getProductStats function from the controller
const testAdminProductStats = async () => {
  try {
    console.log('Testing admin product stats API logic...\n');

    // Get overall product statistics (same logic as in controller)
    const stats = await Product.aggregate([
      {
        $group: {
          _id: null,
          totalProducts: { $sum: 1 },
          activeProducts: {
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          },
          draftProducts: {
            $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] }
          },
          inactiveProducts: {
            $sum: { $cond: [{ $eq: ['$status', 'inactive'] }, 1, 0] }
          },
          archivedProducts: {
            $sum: { $cond: [{ $eq: ['$status', 'archived'] }, 1, 0] }
          },
          featuredProducts: {
            $sum: { $cond: ['$featured', 1, 0] }
          },
          outOfStockProducts: {
            $sum: { $cond: [{ $eq: ['$inventory.stockStatus', 'out_of_stock'] }, 1, 0] }
          },
          lowStockProducts: {
            $sum: { $cond: [{ $lte: ['$inventory.quantity', '$inventory.lowStockThreshold'] }, 1, 0] }
          },
          totalRevenue: { $sum: '$sales.totalRevenue' },
          totalSold: { $sum: '$sales.totalSold' },
          averagePrice: { $avg: '$pricing.basePrice' },
          averageRating: { $avg: '$reviews.averageRating' }
        }
      }
    ]);

    const productStats = stats[0] || {
      totalProducts: 0,
      activeProducts: 0,
      draftProducts: 0,
      inactiveProducts: 0,
      archivedProducts: 0,
      featuredProducts: 0,
      outOfStockProducts: 0,
      lowStockProducts: 0,
      totalRevenue: 0,
      totalSold: 0,
      averagePrice: 0,
      averageRating: 0
    };

    console.log('Product Stats from Admin API logic:');
    console.log(JSON.stringify(productStats, null, 2));

    // Get products by category
    const categoryStats = await Product.aggregate([
      { $match: { status: 'active' } },
      {
        $lookup: {
          from: 'categories',
          localField: 'category',
          foreignField: '_id',
          as: 'categoryInfo'
        }
      },
      { $unwind: '$categoryInfo' },
      {
        $group: {
          _id: '$category',
          categoryName: { $first: '$categoryInfo.name' },
          productCount: { $sum: 1 },
          totalRevenue: { $sum: '$sales.totalRevenue' }
        }
      },
      { $sort: { productCount: -1 } },
      { $limit: 10 }
    ]);

    console.log('\nCategory Stats:');
    console.log(JSON.stringify(categoryStats, null, 2));

    // Get top selling products
    const topSellingProducts = await Product.find({ status: 'active' })
      .populate('vendor', 'businessName')
      .populate('category', 'name')
      .sort({ 'sales.totalSold': -1 })
      .limit(10)
      .select('name sales vendor category pricing')
      .lean();

    console.log('\nTop Selling Products Count:', topSellingProducts.length);

    // Get recent products
    const recentProducts = await Product.find()
      .populate('vendor', 'businessName')
      .populate('category', 'name')
      .sort({ createdAt: -1 })
      .limit(10)
      .select('name status vendor category createdAt')
      .lean();

    console.log('Recent Products Count:', recentProducts.length);

    // Get products needing review (draft status)
    const pendingReview = await Product.countDocuments({ status: 'draft' });

    console.log('Pending Review Count:', pendingReview);

    // Simulate the final API response
    const apiResponse = {
      success: true,
      data: {
        stats: productStats,
        categoryStats,
        topSellingProducts,
        recentProducts,
        pendingReview
      },
      currency: 'INR'
    };

    console.log('\n=== FINAL API RESPONSE ===');
    console.log('Success:', apiResponse.success);
    console.log('Total Products in Response:', apiResponse.data.stats.totalProducts);
    console.log('Active Products in Response:', apiResponse.data.stats.activeProducts);

  } catch (error) {
    console.error('Error testing admin product stats:', error);
  } finally {
    mongoose.connection.close();
  }
};

testAdminProductStats();
