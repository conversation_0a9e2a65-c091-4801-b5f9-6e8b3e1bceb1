import React, { useState, useEffect, useRef } from 'react';
import { SearchOutlined, AppstoreOutlined, SafetyCertificateOutlined, ShoppingCartOutlined, CrownOutlined, CheckCircleOutlined, CreditCardOutlined, GlobalOutlined, UserOutlined, MenuOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useSearch } from '../contexts/SearchContext';
import { Card, CardContent } from './ui/card';
import CategoriesSlider from './CategoriesSlider';
import StreamlineOrdering from "./StreamlineOrdering";
import BentoStyleComponent from './BentoStyleComponent';
import TradeConfidenceComponent from './TradeConfidenceComponent';
import ReadyToStart from "./ReadyToStart";
import Footer from "./Footer";
import CountryFlagSuppliers from "./CountryFlagSuppliers";

const HeroSearchContainer = ({ closeAllModals }) => {
    const [searchInput, setSearchInput] = useState('');
    const searchInputRef = useRef(null);
    const navigate = useNavigate();
    const { 
        suggestions, 
        showSuggestions, 
        setShowSuggestions, 
        getSuggestions,
        setSearchTerm
    } = useSearch();

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (searchInputRef.current && !searchInputRef.current.contains(event.target)) {
                setShowSuggestions(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleSearchInput = (value) => {
        setSearchInput(value);
        if (value.trim().length >= 2) {
            getSuggestions(value);
            setShowSuggestions(true);
        } else {
            setShowSuggestions(false);
        }
    };

    const handleSearch = () => {
        if (searchInput.trim()) {
            setSearchTerm(searchInput.trim());
            navigate(`/search?q=${encodeURIComponent(searchInput.trim())}`);
            setShowSuggestions(false);
            closeAllModals();
        }
    };

    const handleSuggestionClick = (suggestion) => {
        const searchValue = suggestion.title || suggestion.name || suggestion;
        setSearchInput(searchValue);
        setSearchTerm(searchValue);
        navigate(`/search?q=${encodeURIComponent(searchValue)}`);
        setShowSuggestions(false);
        closeAllModals();
    };

    return (
        <div className="w-full max-w-xl mr-auto">
            <div className="relative">
                <div className="flex items-center bg-white rounded-full shadow-lg border border-gray-200 p-1">
                    <div className="relative flex-1" ref={searchInputRef}>
                        <input
                            type="text"
                            placeholder="women's clothing"
                            className="w-full h-12 px-5 text-base border-0 focus:outline-none placeholder:text-gray-400 bg-transparent rounded-l-full"
                            value={searchInput}
                            onChange={(e) => handleSearchInput(e.target.value)}
                            onKeyPress={(e) => {
                                if (e.key === 'Enter') {
                                    handleSearch();
                                }
                            }}
                        />
                        {showSuggestions && suggestions.length > 0 && (
                            <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto">
                                {suggestions.slice(0, 8).map((suggestion, index) => (
                                    <button
                                        key={index}
                                        onClick={() => handleSuggestionClick(suggestion)}
                                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                                    >
                                        <SearchOutlined className="mr-3 text-gray-400" />
                                        <span>{suggestion.title || suggestion.name || suggestion}</span>
                                    </button>
                                ))}
                            </div>
                        )}
                    </div>
                    <button 
                        className="flex items-center justify-center px-6 py-2 bg-orange-500 text-white hover:bg-orange-600 font-medium text-base transition-colors duration-200 rounded-full min-h-[44px]"
                        onClick={handleSearch}
                    >
                        <SearchOutlined className="mr-1.5" />
                        <span>Search</span>
                    </button>
                </div>
            </div>
        </div>
    );
};

const HeroSection = () => {
    const closeAllModals = () => {
        // Empty function for hero section - no modals to close
    };

    return (
        <div className="relative min-h-screen bg-black flex justify-start items-center hero-section-container">
            {/* Background Image and Dark Overlay */}
            <div className="absolute inset-0">
                <div className="absolute inset-0 bg-black/60 z-10"></div>
                <div
                    className="hero-background-image bg-cover bg-center bg-no-repeat absolute inset-0"
                    style={{
                        backgroundImage: 'url("https://i.ibb.co/Nd8v2x66/hero-1.jpg")',
                        backgroundSize: 'cover',
                        backgroundPosition: 'center center',
                        backgroundRepeat: 'no-repeat'
                    }}
                ></div>
            </div>

            {/* Content */}
            <div className="relative z-20 max-w-5xl ml-8 px-4">
                {/* Main Heading */}
                <h1 className="text-white font-bold text-3xl md:text-4xl lg:text-5xl xl:text-6xl mb-12 leading-tight text-left">
                    The leading B2C ecommerce platform for global trade</h1>
                {/* Search Bar */}
                <HeroSearchContainer closeAllModals={closeAllModals} />
            </div>
        </div>
    );
};

// Feature Cards Section Component
// Feature Cards Section Component
const FeatureCardsSection = () => {
    return (
        <section className="py-8 bg-black">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                
                <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
                    <Card className="bg-white border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:transform hover:-translate-y-2 group aspect-square">
                        <CardContent className="p-6 text-left h-full flex flex-col justify-center">
                            <div className="mb-4 flex justify-start">
                                <div className="p-3 bg-blue-50 rounded-lg group-hover:bg-blue-100 transition-colors duration-300">
                                    <AppstoreOutlined className="text-3xl text-blue-600" />
                                </div>
                            </div>
                            <h3 className="text-lg font-bold mb-3 text-gray-900 leading-tight">
                                Millions of business offerings
                            </h3>
                            <p className="text-sm text-gray-600 leading-relaxed">
                                Explore products and suppliers for your business from millions of offerings worldwide.
                            </p>
                        </CardContent>
                    </Card>

                    <Card className="bg-white border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:transform hover:-translate-y-2 group aspect-square">
                        <CardContent className="p-6 text-left h-full flex flex-col justify-center">
                            <div className="mb-4 flex justify-start">
                                <div className="p-3 bg-green-50 rounded-lg group-hover:bg-green-100 transition-colors duration-300">
                                    <SafetyCertificateOutlined className="text-3xl text-green-600" />
                                </div>
                            </div>
                            <h3 className="text-lg font-bold mb-3 text-gray-900 leading-tight">
                                Assured quality and transactions
                            </h3>
                            <p className="text-sm text-gray-600 leading-relaxed">
                                Ensure production quality from verified suppliers, with your orders protected from payment to delivery.
                            </p>
                        </CardContent>
                    </Card>

                    <Card className="bg-white border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:transform hover:-translate-y-2 group aspect-square">
                        <CardContent className="p-6 text-left h-full flex flex-col justify-center">
                            <div className="mb-4 flex justify-start">
                                <div className="p-3 bg-orange-50 rounded-lg group-hover:bg-orange-100 transition-colors duration-300">
                                    <ShoppingCartOutlined className="text-3xl text-orange-600" />
                                </div>
                            </div>
                            <h3 className="text-lg font-bold mb-3 text-gray-900 leading-tight">
                                One-stop trading solution
                            </h3>
                            <p className="text-sm text-gray-600 leading-relaxed">
                                Order seamlessly from product/supplier search to order management, payment, and fulfillment.
                            </p>
                        </CardContent>
                    </Card>

                    <Card className="bg-white border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:transform hover:-translate-y-2 group aspect-square">
                        <CardContent className="p-6 text-left h-full flex flex-col justify-center">
                            <div className="mb-4 flex justify-start">
                                <div className="p-3 bg-purple-50 rounded-lg group-hover:bg-purple-100 transition-colors duration-300">
                                    <CrownOutlined className="text-3xl text-purple-600" />
                                </div>
                            </div>
                            <h3 className="text-lg font-bold mb-3 text-gray-900 leading-tight">
                                Tailored trading experience
                            </h3>
                            <p className="text-sm text-gray-600 leading-relaxed">
                                Get curated benefits, such as exclusive discounts, enhanced protection, and extra support, to help grow your business every step of the way.
                            </p>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </section>
    );
};

// Combined component that exports both HeroSection and FeatureCardsSection
const HeroWithFeatures = () => {
    return (
        <>
            <HeroSection />
            <FeatureCardsSection />
            <CategoriesSlider />
            <TradeConfidenceComponent />
            <StreamlineOrdering />
            <ReadyToStart />
            <BentoStyleComponent />
            <CountryFlagSuppliers />
            <Footer />
        </>
    );
};

export default HeroWithFeatures;
export { HeroSection, FeatureCardsSection };
