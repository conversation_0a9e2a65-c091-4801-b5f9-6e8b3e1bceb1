import React, { useState, useEffect } from 'react';
import CategoryCard from './ui/CategoryCard';
import { productsApi } from '../services/publicApi';
import { useCurrency } from '../contexts/CurrencyContext';
import { getPrimaryProductImage } from '../utils/imageUtils';

const ItemsCard = () => {
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const { getProductPriceInCurrency, getCurrencySymbol, filterProductsByCurrency } = useCurrency();

    useEffect(() => {
        fetchDynamicData();
    }, []);

    const fetchDynamicData = async () => {
        try {
            setLoading(true);

            // Fetch data from all three endpoints
            const [newArrivalsRes, featuredRes, budgetFriendlyRes] = await Promise.all([
                productsApi.getNewArrivals(3), // Get 3 items for responsive display
                productsApi.getFeaturedProducts(3),
                productsApi.getBudgetFriendlyProducts(3)
            ]);

            // Transform product data to match ItemCard format
            const transformProducts = (products, badgeText = "Trending Now") => {
                const filteredProducts = filterProductsByCurrency(products.data?.data?.products || []);
                return filteredProducts.map(product => {
                    const priceData = getProductPriceInCurrency(product);
                    const price = priceData ?
                        `${getCurrencySymbol(priceData.currency)} ${priceData.salePrice || priceData.basePrice || 0}` :
                        'Price not available';

                    return {
                        id: product._id,
                        productId: product._id,
                        image: getPrimaryProductImage(product) || 'https://via.placeholder.com/300x300?text=No+Image',
                        price: price,
                        badgeText: badgeText,
                        alt: product.name || 'Product'
                    };
                });
            };

            const dynamicCategories = [
                {
                    id: 'new-arrivals',
                    title: 'New Arrivals',
                    icon: (
                        <svg
                            className="w-5 h-5 text-gray-600"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                            />
                        </svg>
                    ),
                    items: transformProducts(newArrivalsRes, "New")
                },
                {
                    id: 'top-featured',
                    title: 'Top Featured Products',
                    icon: (
                        <svg
                            className="w-5 h-5 text-gray-600"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                            />
                        </svg>
                    ),
                    items: transformProducts(featuredRes, "Featured")
                },
                {
                    id: 'budget-friendly',
                    title: 'Best Budget Friendly',
                    icon: (
                        <svg
                            className="w-5 h-5 text-gray-600"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    ),
                    items: transformProducts(budgetFriendlyRes, "Budget")
                }
            ];

            setCategories(dynamicCategories);
        } catch (error) {
            console.error('Error fetching dynamic data:', error);
            // Set fallback categories in case of error
            setCategories([]);
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {[1, 2, 3].map(i => (
                        <div key={i} className="bg-white rounded-lg shadow-sm p-4 w-full animate-pulse">
                            <div className="h-6 bg-gray-200 rounded mb-3"></div>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                                {[1, 2, 3].map(j => (
                                    <div key={j} className="bg-gray-200 rounded h-24 sm:h-28 md:h-32"></div>
                                ))}
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    return (
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {categories.map(category => (
                    <CategoryCard
                        key={category.id}
                        title={category.title}
                        icon={category.icon}
                        items={category.items}
                    />
                ))}
            </div>
        </div>
    );
};

export default ItemsCard;