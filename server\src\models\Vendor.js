const mongoose = require('mongoose');

const vendorSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  businessName: {
    type: String,
    required: [true, 'Business name is required'],
    trim: true,
    maxlength: [100, 'Business name cannot exceed 100 characters']
  },
  businessDescription: {
    type: String,
    trim: true,
    maxlength: [1000, 'Business description cannot exceed 1000 characters']
  },
  businessType: {
    type: String,
    enum: ['individual', 'company', 'partnership', 'corporation'],
    required: true
  },
  businessRegistrationNumber: {
    type: String,
    trim: true,
    sparse: true
  },
  taxId: {
    type: String,
    trim: true,
    sparse: true
  },
  logo: {
    type: String,
    default: null
  },
  banner: {
    type: String,
    default: null
  },
  businessAddress: {
    street: {
      type: String,
      required: true,
      trim: true
    },
    city: {
      type: String,
      required: true,
      trim: true
    },
    state: {
      type: String,
      required: true,
      trim: true
    },
    zipCode: {
      type: String,
      required: true,
      trim: true
    },
    country: {
      type: String,
      required: true,
      trim: true
    }
  },
  contactInfo: {
    businessPhone: {
      type: String,
      required: true,
      trim: true
    },
    businessEmail: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    website: {
      type: String,
      trim: true,
      match: [/^https?:\/\/.+/, 'Please enter a valid website URL']
    },
    socialMedia: {
      facebook: String,
      twitter: String,
      instagram: String,
      linkedin: String
    }
  },
  bankDetails: {
    accountHolderName: {
      type: String,
      required: true,
      trim: true
    },
    bankName: {
      type: String,
      required: true,
      trim: true
    },
    accountNumber: {
      type: String,
      required: true,
      trim: true
    },
    routingNumber: {
      type: String,
      required: true,
      trim: true
    },
    accountType: {
      type: String,
      enum: ['checking', 'savings'],
      required: true
    }
  },
  verification: {
    status: {
      type: String,
      enum: ['pending', 'verified', 'rejected', 'suspended'],
      default: 'pending'
    },
    documents: [{
      type: {
        type: String,
        enum: ['business_license', 'tax_certificate', 'identity_proof', 'address_proof', 'bank_statement'],
        required: true
      },
      url: {
        type: String,
        required: true
      },
      status: {
        type: String,
        enum: ['pending', 'approved', 'rejected'],
        default: 'pending'
      },
      rejectionReason: String,
      uploadedAt: {
        type: Date,
        default: Date.now
      },
      reviewedAt: Date,
      reviewedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    }],
    verifiedAt: Date,
    verifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    rejectionReason: String
  },
  subscription: {
    plan: {
      type: String,
      enum: ['basic', 'premium', 'enterprise'],
      default: 'basic'
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'suspended', 'cancelled'],
      default: 'active'
    },
    startDate: {
      type: Date,
      default: Date.now
    },
    endDate: Date,
    features: {
      maxProducts: {
        type: Number,
        default: 100
      },
      maxImages: {
        type: Number,
        default: 5
      },
      analyticsAccess: {
        type: Boolean,
        default: false
      },
      prioritySupport: {
        type: Boolean,
        default: false
      },
      customBranding: {
        type: Boolean,
        default: false
      }
    }
  },
  performance: {
    rating: {
      type: Number,
      min: 0,
      max: 5,
      default: 0
    },
    totalReviews: {
      type: Number,
      default: 0
    },
    totalSales: {
      type: Number,
      default: 0
    },
    totalRevenue: {
      type: Number,
      default: 0
    },
    totalProducts: {
      type: Number,
      default: 0
    },
    totalOrders: {
      type: Number,
      default: 0
    },
    responseTime: {
      type: Number, // in hours
      default: 24
    },
    fulfillmentRate: {
      type: Number, // percentage
      default: 100
    },
    monthlyStats: [{
      month: {
        type: String,
        required: true
      },
      year: {
        type: Number,
        required: true
      },
      sales: {
        type: Number,
        default: 0
      },
      revenue: {
        type: Number,
        default: 0
      },
      orders: {
        type: Number,
        default: 0
      },
      newProducts: {
        type: Number,
        default: 0
      }
    }],
    qualityScore: {
      type: Number,
      min: 0,
      max: 100,
      default: 75
    },
    customerSatisfaction: {
      type: Number,
      min: 0,
      max: 100,
      default: 85
    }
  },
  commission: {
    rate: {
      type: Number,
      min: 0,
      max: 100,
      default: 15 // 15% default commission
    },
    type: {
      type: String,
      enum: ['percentage', 'fixed'],
      default: 'percentage'
    },
    fixedAmount: {
      type: Number,
      default: 0
    },
    totalEarned: {
      type: Number,
      default: 0
    },
    totalPaid: {
      type: Number,
      default: 0
    },
    pendingAmount: {
      type: Number,
      default: 0
    },
    lastPayoutDate: Date,
    payoutHistory: [{
      amount: {
        type: Number,
        required: true
      },
      date: {
        type: Date,
        default: Date.now
      },
      method: {
        type: String,
        enum: ['bank_transfer', 'paypal', 'stripe', 'check'],
        required: true
      },
      transactionId: String,
      status: {
        type: String,
        enum: ['pending', 'completed', 'failed'],
        default: 'pending'
      }
    }]
  },
  onboarding: {
    status: {
      type: String,
      enum: ['not_started', 'in_progress', 'completed'],
      default: 'not_started'
    },
    completedSteps: [{
      step: {
        type: String,
        enum: ['profile_setup', 'business_info', 'documents_upload', 'bank_details', 'first_product', 'store_setup'],
        required: true
      },
      completedAt: {
        type: Date,
        default: Date.now
      }
    }],
    startedAt: Date,
    completedAt: Date,
    currentStep: {
      type: String,
      enum: ['profile_setup', 'business_info', 'documents_upload', 'bank_details', 'first_product', 'store_setup'],
      default: 'profile_setup'
    },
    progress: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    }
  },
  settings: {
    autoAcceptOrders: {
      type: Boolean,
      default: false
    },
    processingTime: {
      type: Number, // in days
      default: 2
    },
    currency: {
      type: String,
      enum: ['USD', 'EUR', 'GBP', 'INR', 'CAD', 'AUD', 'JPY', 'CNY'],
      default: 'INR'
    },
    returnPolicy: {
      type: String,
      maxlength: [500, 'Return policy cannot exceed 500 characters']
    },
    shippingPolicy: {
      type: String,
      maxlength: [500, 'Shipping policy cannot exceed 500 characters']
    },
    businessHours: {
      monday: { open: String, close: String, closed: { type: Boolean, default: false } },
      tuesday: { open: String, close: String, closed: { type: Boolean, default: false } },
      wednesday: { open: String, close: String, closed: { type: Boolean, default: false } },
      thursday: { open: String, close: String, closed: { type: Boolean, default: false } },
      friday: { open: String, close: String, closed: { type: Boolean, default: false } },
      saturday: { open: String, close: String, closed: { type: Boolean, default: false } },
      sunday: { open: String, close: String, closed: { type: Boolean, default: true } }
    },
    notifications: {
      newOrders: { type: Boolean, default: true },
      lowStock: { type: Boolean, default: true },
      reviews: { type: Boolean, default: true },
      messages: { type: Boolean, default: true }
    }
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'pending_approval'],
    default: 'pending_approval'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes (user index is automatically created by unique: true)
vendorSchema.index({ businessName: 1 });
vendorSchema.index({ 'verification.status': 1 });
vendorSchema.index({ status: 1 });
vendorSchema.index({ 'performance.rating': -1 });
vendorSchema.index({ createdAt: -1 });

// Virtual for verification completion percentage
vendorSchema.virtual('verificationProgress').get(function() {
  const requiredDocs = ['business_license', 'tax_certificate', 'identity_proof'];
  const uploadedDocs = (this.verification && this.verification.documents) ? 
    this.verification.documents.map(doc => doc.type) : [];
  const completedDocs = requiredDocs.filter(doc => uploadedDocs.includes(doc));
  return Math.round((completedDocs.length / requiredDocs.length) * 100);
});

// Virtual for subscription status
vendorSchema.virtual('isSubscriptionActive').get(function() {
  return this.subscription.status === 'active' && 
         (!this.subscription.endDate || this.subscription.endDate > new Date());
});

// Static method to get vendor statistics
vendorSchema.statics.getStatistics = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: null,
        totalVendors: { $sum: 1 },
        activeVendors: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        pendingVendors: {
          $sum: { $cond: [{ $eq: ['$status', 'pending_approval'] }, 1, 0] }
        },
        suspendedVendors: {
          $sum: { $cond: [{ $eq: ['$status', 'suspended'] }, 1, 0] }
        },
        verifiedVendors: {
          $sum: { $cond: [{ $eq: ['$verification.status', 'verified'] }, 1, 0] }
        },
        totalRevenue: { $sum: '$performance.totalRevenue' },
        averageRating: { $avg: '$performance.rating' },
        totalProducts: { $sum: '$performance.totalProducts' },
        totalOrders: { $sum: '$performance.totalOrders' },
        totalPendingCommission: { $sum: '$commission.pendingAmount' },
        totalCommissionEarned: { $sum: '$commission.totalEarned' },
        totalCommissionPaid: { $sum: '$commission.totalPaid' }
      }
    }
  ]);
  
  return stats[0] || {
    totalVendors: 0,
    activeVendors: 0,
    pendingVendors: 0,
    suspendedVendors: 0,
    verifiedVendors: 0,
    totalRevenue: 0,
    averageRating: 0,
    totalProducts: 0,
    totalOrders: 0,
    totalPendingCommission: 0,
    totalCommissionEarned: 0,
    totalCommissionPaid: 0
  };
};

// Static method to get top performing vendors
vendorSchema.statics.getTopPerformers = function(limit = 10) {
  return this.find({ status: 'active' })
    .populate('user', 'firstName lastName email')
    .sort({ 'performance.rating': -1, 'performance.totalRevenue': -1 })
    .limit(limit)
    .select('businessName performance user verification.status');
};

// Static method to get recent vendor registrations
vendorSchema.statics.getRecentRegistrations = function(days = 7) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          $dateToString: {
            format: '%Y-%m-%d',
            date: '$createdAt'
          }
        },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { _id: 1 }
    }
  ]);
};

// Instance method to calculate commission
vendorSchema.methods.calculateCommission = function(orderAmount) {
  if (this.commission.type === 'percentage') {
    return (orderAmount * this.commission.rate) / 100;
  } else {
    return this.commission.rate;
  }
};

// Instance method to update performance metrics
vendorSchema.methods.updatePerformance = async function(metrics) {
  const updates = {};

  if (metrics.newSale) {
    updates.$inc = {
      'performance.totalSales': 1,
      'performance.totalRevenue': metrics.saleAmount || 0
    };
  }

  if (metrics.newOrder) {
    updates.$inc = { ...updates.$inc, 'performance.totalOrders': 1 };
  }

  if (metrics.newProduct) {
    updates.$inc = { ...updates.$inc, 'performance.totalProducts': 1 };
  }

  if (metrics.rating) {
    // Recalculate average rating
    const currentTotal = this.performance.rating * this.performance.totalReviews;
    const newTotal = currentTotal + metrics.rating;
    const newCount = this.performance.totalReviews + 1;

    updates.$set = {
      'performance.rating': newTotal / newCount,
      'performance.totalReviews': newCount
    };
  }

  return this.updateOne(updates);
};

// Instance method to complete onboarding step
vendorSchema.methods.completeOnboardingStep = function(step) {
  // Check if step is already completed
  const existingStep = this.onboarding.completedSteps.find(s => s.step === step);
  if (existingStep) {
    return this;
  }

  // Add completed step
  this.onboarding.completedSteps.push({
    step: step,
    completedAt: new Date()
  });

  // Update progress
  const totalSteps = 6; // profile_setup, business_info, documents_upload, bank_details, first_product, store_setup
  const completedCount = this.onboarding.completedSteps.length;
  this.onboarding.progress = Math.round((completedCount / totalSteps) * 100);

  // Update status
  if (this.onboarding.status === 'not_started') {
    this.onboarding.status = 'in_progress';
    this.onboarding.startedAt = new Date();
  }

  if (completedCount === totalSteps) {
    this.onboarding.status = 'completed';
    this.onboarding.completedAt = new Date();
  }

  // Update current step to next step
  const stepOrder = ['profile_setup', 'business_info', 'documents_upload', 'bank_details', 'first_product', 'store_setup'];
  const currentIndex = stepOrder.indexOf(step);
  if (currentIndex < stepOrder.length - 1) {
    this.onboarding.currentStep = stepOrder[currentIndex + 1];
  }

  return this;
};

// Instance method to record commission payout
vendorSchema.methods.recordPayout = function(amount, method, transactionId) {
  this.commission.payoutHistory.push({
    amount: amount,
    date: new Date(),
    method: method,
    transactionId: transactionId,
    status: 'completed'
  });

  this.commission.totalPaid += amount;
  this.commission.pendingAmount = Math.max(0, this.commission.pendingAmount - amount);
  this.commission.lastPayoutDate = new Date();

  return this.save();
};

// Instance method to add commission earnings
vendorSchema.methods.addCommissionEarnings = function(amount) {
  this.commission.totalEarned += amount;
  this.commission.pendingAmount += amount;

  return this.save();
};

// Instance method to verify vendor
vendorSchema.methods.verifyVendor = function(adminId, notes = '') {
  this.verification.status = 'verified';
  this.verification.verifiedAt = new Date();
  this.verification.verifiedBy = adminId;
  this.status = 'active';

  // Mark all documents as approved
  this.verification.documents.forEach(doc => {
    if (doc.status === 'pending') {
      doc.status = 'approved';
      doc.reviewedAt = new Date();
      doc.reviewedBy = adminId;
    }
  });

  return this.save();
};

// Instance method to reject vendor verification
vendorSchema.methods.rejectVerification = function(adminId, reason, notes = '') {
  this.verification.status = 'rejected';
  this.verification.rejectionReason = reason;
  this.status = 'inactive';

  return this.save();
};

// Static method to get vendors pending verification
vendorSchema.statics.getPendingVerification = function(limit = 20) {
  return this.find({ 'verification.status': 'pending' })
    .populate('user', 'firstName lastName email')
    .sort({ createdAt: 1 })
    .limit(limit);
};

module.exports = mongoose.model('Vendor', vendorSchema);