import React, { useState, useEffect } from 'react'
import { Search, ShoppingCart, Globe, ChevronDown, User } from 'lucide-react'
import LocationModal from './navbar/LocationModal'
import LanguageCurrencyModal from './navbar/LanguageCurrencyModal'
import CartHoverModal from './navbar/CartHoverModal'
import SignInHoverModal from './navbar/SignInHoverModal'

const NewNavbar = ({ noShadow = false }) => {
    const [searchQuery, setSearchQuery] = useState('')
    const [showLocationModal, setShowLocationModal] = useState(false)
    const [selectedCountry, setSelectedCountry] = useState({ code: 'IN', name: 'India' })
    const [postalCode, setPostalCode] = useState('')
    const [isLoggedIn, setIsLoggedIn] = useState(false) // This should come from your auth state
    const [language, setLanguage] = useState('English')
    const [currency, setCurrency] = useState('USD - US Dollar')
    const [showLangCurrencyModal, setShowLangCurrencyModal] = useState(false)
    const [showCartModal, setShowCartModal] = useState(false)
    const [showSignInModal, setShowSignInModal] = useState(false)
    const [showSearchBar, setShowSearchBar] = useState(false)
    const [isScrolled, setIsScrolled] = useState(false)
    // Get saved data from localStorage on component mount
    useEffect(() => {
        const savedCountry = localStorage.getItem('selectedCountry')
        const savedPostalCode = localStorage.getItem('postalCode')
        const savedLanguage = localStorage.getItem('selectedLanguage')
        const savedCurrency = localStorage.getItem('selectedCurrency')
        
        if (savedCountry) {
            setSelectedCountry(JSON.parse(savedCountry))
        }
        if (savedPostalCode) {
            setPostalCode(savedPostalCode)
        }
        if (savedLanguage) {
            setLanguage(savedLanguage)
        }
        if (savedCurrency) {
            setCurrency(savedCurrency)
        }
    }, [])

    useEffect(() => {
        const handleScroll = () => {
            const scrollPosition = window.scrollY
            const windowHeight = window.innerHeight
            const documentHeight = document.documentElement.scrollHeight
            const scrollPercentage = (scrollPosition / (documentHeight - windowHeight)) * 100

            // Show search bar when scrolled 10% or more
            setShowSearchBar(scrollPercentage >= 10)
            // Change navbar background when scrolled
            setIsScrolled(scrollPosition > 50)
        }

        window.addEventListener('scroll', handleScroll)

        return () => {
            window.removeEventListener('scroll', handleScroll)
        }
    }, [])
    
    // Auto-close modals with delay when mouse leaves the area
    const [hoverTimeout, setHoverTimeout] = useState(null)
    
    const handleMouseEnter = (modalType) => {
        if (hoverTimeout) {
            clearTimeout(hoverTimeout)
            setHoverTimeout(null)
        }
        
        // Close all other modals first
        setShowLocationModal(false)
        setShowLangCurrencyModal(false)
        setShowCartModal(false)
        setShowSignInModal(false)
        
        // Then open the requested modal
        if (modalType === 'location') {
            setShowLocationModal(true)
        } else if (modalType === 'langCurrency') {
            setShowLangCurrencyModal(true)
        } else if (modalType === 'cart') {
            setShowCartModal(true)
        } else if (modalType === 'signIn') {
            setShowSignInModal(true)
        }
    }
    
    const handleMouseLeave = (modalType) => {
        // Instant closing for cart and sign-in modals
        if (modalType === 'cart') {
            setShowCartModal(false)
        } else if (modalType === 'signIn') {
            setShowSignInModal(false)
        } else {
            // Delayed closing for location and language modals
            const timeout = setTimeout(() => {
                if (modalType === 'location') {
                    setShowLocationModal(false)
                } else if (modalType === 'langCurrency') {
                    setShowLangCurrencyModal(false)
                }
            }, 200) // 200ms delay before closing
            setHoverTimeout(timeout)
        }
    }
    
    // Cleanup timeout on unmount
    useEffect(() => {
        return () => {
            if (hoverTimeout) {
                clearTimeout(hoverTimeout)
            }
        }
    }, [hoverTimeout])
    

    return (
        <div className={`fixed top-0 left-0 w-full z-50 transition-all duration-300 ${isScrolled ? 'bg-white shadow-md' : 'navbar-transparent'}`}>
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex items-center justify-between h-16">
                    {/* Logo */}
                    <div className="flex items-center">
                        <img
                            src="https://res.cloudinary.com/alicartify/image/upload/v1753779437/logo_lksntc.png"
                            alt="Logo"
                            className="h-8 w-auto navbar-no-shadow"
                        />
                    </div>

                    {/* Search Bar - Only show when scrolled 15% */}
                    {showSearchBar && (
                        <div className="flex-1 max-w-2xl mx-8 transition-all duration-300 ease-in-out">
                            <div className="relative">
                                <input
                                    type="text"
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    placeholder="spider hoodie"
                                    className="w-full px-4 py-2 pr-20 border border-gray-300 rounded-3xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                />
                                <button className="absolute right-1 top-1 bottom-1 px-6 bg-orange-500 text-white rounded-3xl hover:bg-orange-600 transition-colors">
                                    Search
                                </button>
                            </div>
                        </div>
                    )}

                    {/* Right Side Navigation */}
                    <div className="flex items-center space-x-6">
                        {/* Deliver to */}
                        <div
                            className={`relative flex items-center cursor-pointer text-sm transition-colors duration-300 ${isScrolled ? 'text-gray-700' : 'navbar-text-element'}`}
                            onMouseEnter={() => handleMouseEnter('location')}
                            onMouseLeave={() => handleMouseLeave('location')}
                        >
                            <span className="mr-1">Deliver to:</span>
                            <div className="flex items-center">
                                <img
                                    src={`https://flagcdn.com/w20/${selectedCountry.code.toLowerCase()}.png`}
                                    alt={`${selectedCountry.name} flag`}
                                    className="w-4 h-3 mr-1"
                                />
                                <span className="font-medium">{selectedCountry.code}</span>
                            </div>
                            
                            <LocationModal 
                                showModal={showLocationModal}
                                setShowModal={setShowLocationModal}
                                selectedCountry={selectedCountry}
                                setSelectedCountry={setSelectedCountry}
                                postalCode={postalCode}
                                setPostalCode={setPostalCode}
                                isLoggedIn={isLoggedIn}
                                currency={currency}
                                setCurrency={setCurrency}
                                onMouseEnter={() => handleMouseEnter('location')}
                                onMouseLeave={() => handleMouseLeave('location')}
                            />
                        </div>

                        {/* Language & Currency */}
                        <div
                            className={`relative flex items-center cursor-pointer text-sm ml-6 transition-colors duration-300 ${isScrolled ? 'text-gray-700' : 'navbar-text-element'}`}
                            onMouseEnter={() => handleMouseEnter('langCurrency')}
                            onMouseLeave={() => handleMouseLeave('langCurrency')}
                        >
                            <Globe className="w-4 h-4 mr-1" />
                            <span>{language}-{currency.split(' ')[0]}</span>
                            <ChevronDown className="w-4 h-4 ml-1" />
                            
                            <LanguageCurrencyModal 
                                showModal={showLangCurrencyModal}
                                setShowModal={setShowLangCurrencyModal}
                                language={language}
                                setLanguage={setLanguage}
                                currency={currency}
                                setCurrency={setCurrency}
                                selectedCountry={selectedCountry}
                                setSelectedCountry={setSelectedCountry}
                                onMouseEnter={() => handleMouseEnter('langCurrency')}
                                onMouseLeave={() => handleMouseLeave('langCurrency')}
                            />
                        </div>

                        {/* Shopping Cart */}
                        <div 
                            className="relative"
                            onMouseEnter={() => handleMouseEnter('cart')}
                            onMouseLeave={() => handleMouseLeave('cart')}
                        >
                            <button
                                className={`transition-colors duration-300 ${isScrolled ? 'text-gray-700' : 'navbar-text-element'}`}
                            >
                                <ShoppingCart className="w-6 h-6" />
                            </button>
                            
                            <CartHoverModal 
                                showModal={showCartModal}
                                onMouseEnter={() => handleMouseEnter('cart')}
                                onMouseLeave={() => handleMouseLeave('cart')}
                            />
                        </div>

                        {/* Sign In */}
                        <div 
                            className="relative bg-white"
                            onMouseEnter={() => handleMouseEnter('signIn')}
                            onMouseLeave={() => handleMouseLeave('signIn')}
                        >
                            <button
                                className={`flex items-center text-sm transition-colors duration-300 ${isScrolled ? 'text-gray-700' : 'navbar-text-element'}`}
                            >
                                <User className="w-4 h-4 mr-1" />
                                <span>Sign in</span>
                            </button>
                            
                            <SignInHoverModal 
                                showModal={showSignInModal}
                                onMouseEnter={() => handleMouseEnter('signIn')}
                                onMouseLeave={() => handleMouseLeave('signIn')}
                            />
                        </div>

                        {/* Create Account Button */}
                        <button className="bg-orange-500 text-white px-4 py-2 rounded-3xl text-sm font-medium hover:bg-orange-600 transition-colors">
                            Create account
                        </button>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default NewNavbar
