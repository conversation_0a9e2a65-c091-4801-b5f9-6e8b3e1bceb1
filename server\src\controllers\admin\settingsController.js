const { Setting } = require('../../models');

/**
 * Get all system settings
 */
const getSettings = async (req, res) => {
  try {
    const settings = await Setting.find({}).lean();

    // Convert array to object for easier frontend consumption
    const settingsObject = {};
    settings.forEach(setting => {
      settingsObject[setting.key] = {
        value: setting.value,
        type: setting.type,
        description: setting.description,
        category: setting.category
      };
    });

    res.json({
      success: true,
      data: settingsObject
    });

  } catch (error) {
    console.error('Get settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch settings',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get settings by category
 */
const getSettingsByCategory = async (req, res) => {
  try {
    const { category } = req.params;

    const settings = await Setting.find({ category }).lean();

    // Convert array to object
    const settingsObject = {};
    settings.forEach(setting => {
      settingsObject[setting.key] = {
        value: setting.value,
        type: setting.type,
        description: setting.description
      };
    });

    res.json({
      success: true,
      data: settingsObject
    });

  } catch (error) {
    console.error('Get settings by category error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch settings',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update system settings
 */
const updateSettings = async (req, res) => {
  try {
    const { settings } = req.body;

    if (!settings || typeof settings !== 'object') {
      return res.status(400).json({
        success: false,
        message: 'Settings object is required'
      });
    }

    const updatePromises = [];

    // Update each setting
    for (const [key, value] of Object.entries(settings)) {
      updatePromises.push(
        Setting.findOneAndUpdate(
          { key },
          {
            key,
            value,
            updatedAt: new Date(),
            updatedBy: req.user.userId
          },
          {
            upsert: true,
            new: true,
            setDefaultsOnInsert: true
          }
        )
      );
    }

    await Promise.all(updatePromises);

    res.json({
      success: true,
      message: 'Settings updated successfully'
    });

  } catch (error) {
    console.error('Update settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update settings',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update single setting
 */
const updateSetting = async (req, res) => {
  try {
    const { key } = req.params;
    const { value, type, description, category } = req.body;

    const updateData = {
      key,
      value,
      updatedAt: new Date(),
      updatedBy: req.user.userId
    };

    if (type) updateData.type = type;
    if (description) updateData.description = description;
    if (category) updateData.category = category;

    const setting = await Setting.findOneAndUpdate(
      { key },
      updateData,
      {
        upsert: true,
        new: true,
        setDefaultsOnInsert: true
      }
    );

    res.json({
      success: true,
      data: setting,
      message: 'Setting updated successfully'
    });

  } catch (error) {
    console.error('Update setting error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update setting',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Delete setting
 */
const deleteSetting = async (req, res) => {
  try {
    const { key } = req.params;

    const setting = await Setting.findOneAndDelete({ key });

    if (!setting) {
      return res.status(404).json({
        success: false,
        message: 'Setting not found'
      });
    }

    res.json({
      success: true,
      message: 'Setting deleted successfully'
    });

  } catch (error) {
    console.error('Delete setting error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete setting',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Initialize default settings
 */
const initializeDefaultSettings = async (req, res) => {
  try {
    const defaultSettings = [
      // General Settings
      { key: 'site_name', value: 'Multi-Vendor eCommerce', type: 'string', category: 'general', description: 'Site name' },
      { key: 'site_description', value: 'Your ultimate shopping destination', type: 'string', category: 'general', description: 'Site description' },
      { key: 'currency', value: 'USD', type: 'string', category: 'general', description: 'Default currency' },
      { key: 'timezone', value: 'UTC', type: 'string', category: 'general', description: 'Default timezone' },
      { key: 'maintenance_mode', value: false, type: 'boolean', category: 'general', description: 'Maintenance mode' },
      { key: 'user_registration', value: true, type: 'boolean', category: 'general', description: 'Allow user registration' },
      { key: 'vendor_registration', value: true, type: 'boolean', category: 'general', description: 'Allow vendor registration' },
      { key: 'guest_checkout', value: true, type: 'boolean', category: 'general', description: 'Allow guest checkout' },

      // Email Settings
      { key: 'smtp_host', value: '', type: 'string', category: 'email', description: 'SMTP host' },
      { key: 'smtp_port', value: 587, type: 'number', category: 'email', description: 'SMTP port' },
      { key: 'smtp_username', value: '', type: 'string', category: 'email', description: 'SMTP username' },
      { key: 'smtp_password', value: '', type: 'string', category: 'email', description: 'SMTP password' },
      { key: 'from_email', value: '', type: 'string', category: 'email', description: 'From email address' },
      { key: 'from_name', value: 'Multi-Vendor eCommerce', type: 'string', category: 'email', description: 'From name' },

      // Payment Settings
      { key: 'stripe_publishable_key', value: '', type: 'string', category: 'payment', description: 'Stripe publishable key' },
      { key: 'stripe_secret_key', value: '', type: 'string', category: 'payment', description: 'Stripe secret key' },
      { key: 'paypal_client_id', value: '', type: 'string', category: 'payment', description: 'PayPal client ID' },
      { key: 'paypal_client_secret', value: '', type: 'string', category: 'payment', description: 'PayPal client secret' },

      // Security Settings
      { key: 'session_timeout', value: 30, type: 'number', category: 'security', description: 'Session timeout in minutes' },
      { key: 'password_min_length', value: 8, type: 'number', category: 'security', description: 'Minimum password length' },
      { key: 'max_login_attempts', value: 5, type: 'number', category: 'security', description: 'Maximum login attempts' },
      { key: 'account_lockout_time', value: 15, type: 'number', category: 'security', description: 'Account lockout time in minutes' },

      // Vendor Settings
      { key: 'vendor_commission_rate', value: 10, type: 'number', category: 'vendor', description: 'Default vendor commission rate (%)' },
      { key: 'vendor_auto_approval', value: false, type: 'boolean', category: 'vendor', description: 'Auto approve vendor registrations' },
      { key: 'product_auto_approval', value: false, type: 'boolean', category: 'vendor', description: 'Auto approve vendor products' }
    ];

    const initPromises = defaultSettings.map(setting =>
      Setting.findOneAndUpdate(
        { key: setting.key },
        {
          ...setting,
          createdAt: new Date(),
          updatedAt: new Date(),
          updatedBy: req.user.userId
        },
        {
          upsert: true,
          new: true,
          setDefaultsOnInsert: true
        }
      )
    );

    await Promise.all(initPromises);

    res.json({
      success: true,
      message: 'Default settings initialized successfully'
    });

  } catch (error) {
    console.error('Initialize default settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initialize default settings',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getSettings,
  getSettingsByCategory,
  updateSettings,
  updateSetting,
  deleteSetting,
  initializeDefaultSettings
};