const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Product = require('./src/models/Product');
const Vendor = require('./src/models/Vendor');
const User = require('./src/models/User');

async function testProductStats() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('Connected to MongoDB');

    // Test the Product.getStatistics() method
    console.log('Testing Product.getStatistics()...');
    const stats = await Product.getStatistics();
    console.log('Product statistics result:', JSON.stringify(stats, null, 2));

    // Also test a direct count to compare
    console.log('\nTesting direct count...');
    const totalCount = await Product.countDocuments({});
    console.log('Total products in DB:', totalCount);

    const activeCount = await Product.countDocuments({ status: 'active' });
    console.log('Active products in DB:', activeCount);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Connection closed');
  }
}

testProductStats();
