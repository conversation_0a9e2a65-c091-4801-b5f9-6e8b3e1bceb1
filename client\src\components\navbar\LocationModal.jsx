import React, { useState, useEffect, useRef } from 'react'
import { X, ChevronDown } from 'lucide-react'
import countries from 'world-countries'
import currencies from 'currency-codes'

const LocationModal = ({ 
    showModal, 
    setShowModal, 
    selectedCountry, 
    setSelectedCountry,
    postalCode,
    setPostalCode,
    isLoggedIn,
    currency,
    setCurrency,
    onMouseEnter,
    onMouseLeave 
}) => {
    const [showCountryDropdown, setShowCountryDropdown] = useState(false)
    const countryDropdownRef = useRef(null)

    // Close country dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (countryDropdownRef.current && !countryDropdownRef.current.contains(event.target) &&
                !event.target.closest('[data-country-selector]')) {
                setShowCountryDropdown(false)
            }
        }
        
        if (showCountryDropdown) {
            document.addEventListener('mousedown', handleClickOutside)
        }
        
        return () => {
            document.removeEventListener('mousedown', handleClickOutside)
        }
    }, [showCountryDropdown])

    const handleCountrySelect = (country) => {
        const countryData = { code: country.cca2, name: country.name.common }
        setSelectedCountry(countryData)
        localStorage.setItem('selectedCountry', JSON.stringify(countryData))
        
        // Update currency based on selected country
        if (country.currencies && setCurrency) {
            const currencyCode = Object.keys(country.currencies)[0]
            const currencyInfo = currencies.data.find(curr => curr.code === currencyCode)
            if (currencyInfo) {
                const currencyString = `${currencyInfo.code} - ${currencyInfo.currency}`
                setCurrency(currencyString)
                localStorage.setItem('selectedCurrency', currencyString)
            }
        }
    }

    const handleSaveLocation = () => {
        localStorage.setItem('postalCode', postalCode)
        setShowModal(false)
    }

    const handleSignInToAddAddress = () => {
        console.log('Sign in to add address')
        setShowModal(false)
    }

    if (!showModal) return null

    return (
        <div 
            className={`absolute top-full right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-4 transform transition-all duration-300 ease-in-out ${
                showModal 
                    ? 'opacity-100 translate-y-0 scale-100' 
                    : 'opacity-0 translate-y-2 scale-95 pointer-events-none'
            }`}
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
        >
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Specify your location</h3>
                <button 
                    onClick={() => setShowModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                >
                    <X className="w-5 h-5" />
                </button>
            </div>
            
            <p className="text-sm text-gray-600 mb-4">
                Shipping options and fees vary based on your location
            </p>
            
            {!isLoggedIn ? (
                <>
                    <button 
                        onClick={handleSignInToAddAddress}
                        className="w-full bg-orange-500 text-white py-2 px-4 rounded-md hover:bg-orange-600 transition-colors mb-4"
                    >
                        Sign in to add address
                    </button>
                    <div className="text-center text-sm text-gray-500 mb-4">or</div>
                </>
            ) : null}
            
            {/* Country Dropdown */}
            <div className="mb-4 relative">
                <div 
                    data-country-selector
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 bg-white cursor-pointer flex items-center justify-between"
                    onClick={() => setShowCountryDropdown(!showCountryDropdown)}
                >
                    <div className="flex items-center">
                        <img 
                            src={`https://flagcdn.com/w20/${selectedCountry.code.toLowerCase()}.png`}
                            alt={`${selectedCountry.name} flag`}
                            className="w-4 h-3 mr-2"
                        />
                        <span>{selectedCountry.name}</span>
                    </div>
                    <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${
                        showCountryDropdown ? 'rotate-180' : ''
                    }`} />
                </div>
                
                {/* Custom Dropdown */}
                {showCountryDropdown && (
                    <div 
                        ref={countryDropdownRef}
                        className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto"
                    >
                        {countries.map((country) => (
                            <div 
                                key={country.cca2}
                                className="flex items-center p-2 hover:bg-gray-100 cursor-pointer"
                                onClick={() => {
                                    handleCountrySelect(country)
                                    setShowCountryDropdown(false)
                                }}
                            >
                                <img 
                                    src={`https://flagcdn.com/w20/${country.cca2.toLowerCase()}.png`}
                                    alt={`${country.name.common} flag`}
                                    className="w-4 h-3 mr-2"
                                />
                                <span>{country.name.common}</span>
                            </div>
                        ))}
                    </div>
                )}
            </div>
            
            {/* Postal Code Input */}
            <div className="mb-4">
                <input 
                    type="text"
                    placeholder="Enter ZIP or postal code"
                    value={postalCode}
                    onChange={(e) => setPostalCode(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                />
            </div>
            
            {/* Save Button */}
            <button 
                onClick={handleSaveLocation}
                className="w-full bg-orange-500 text-white py-2 px-4 rounded-md hover:bg-orange-600 transition-colors"
            >
                Save
            </button>
        </div>
    )
}

export default LocationModal
