# Database Migration Guide

This guide will help you migrate your local MongoDB data to your production MongoDB Atlas database.

## Prerequisites

1. **Local MongoDB** must be running with your development data
2. **Production MongoDB Atlas** connection must be configured in `.env`
3. **Internet connection** for Atlas access
4. **Backup space** for local data export

## Migration Process

### Step 1: Test Database Connections

Before starting the migration, verify that both your local and production databases are accessible:

```bash
cd server
npm run test:connections
```

This will:
- Test connection to local MongoDB
- Test connection to production Atlas
- Show collection counts and data sizes
- Verify credentials and network access

### Step 2: Create Backup (Recommended)

Create a backup of your local data before migration:

```bash
npm run migrate:backup
```

This will:
- Export all collections to JSON files
- Save backups in `server/backups/` directory
- Preserve original data structure

### Step 3: Run Migration

Migrate your local data to production:

```bash
npm run migrate:prod
```

The migration process will:
- Connect to both databases
- Show you what will be migrated
- Ask for confirmation before proceeding
- Export data from local MongoDB
- Import data to production Atlas
- Handle large datasets with batch processing
- Show progress for each collection

### Step 4: Verify Migration

After migration, verify the data was transferred correctly:

```bash
npm run migrate:verify
```

This will:
- Compare document counts between local and production
- Report any discrepancies
- Confirm successful migration

## Migration Features

### ✅ What Gets Migrated

- **All Collections**: Users, products, orders, categories, etc.
- **Document Structure**: Preserves all fields and data types
- **Relationships**: Maintains ObjectId references
- **Indexes**: You may need to recreate indexes manually

### ⚠️ Important Notes

1. **Existing Data**: The script will ask before overwriting existing collections
2. **Large Datasets**: Uses batch processing to handle large collections
3. **Network**: Ensure stable internet connection for Atlas
4. **Backup**: Always creates local backups before migration
5. **Downtime**: Consider maintenance window for production migration

### 🔧 Manual Commands

You can also run the migration script directly with different options:

```bash
# Full migration
node scripts/migrate-to-production.js migrate

# Backup only
node scripts/migrate-to-production.js backup-only

# Verify migration
node scripts/migrate-to-production.js verify
```

## Troubleshooting

### Connection Issues

If you get connection errors:

1. **Local MongoDB**: Ensure MongoDB service is running
   ```bash
   # Windows
   net start MongoDB
   
   # macOS/Linux
   sudo systemctl start mongod
   ```

2. **Atlas Connection**: Check your `.env` file for correct credentials
   - Verify username/password
   - Check IP whitelist in Atlas
   - Ensure network access

### Memory Issues

For large datasets:
- The script uses batch processing (1000 documents per batch)
- Monitor system memory during migration
- Consider migrating collections individually if needed

### Data Conflicts

If collections already exist in production:
- The script will ask for confirmation before overwriting
- Choose 'yes' to replace existing data
- Choose 'no' to skip that collection

## Post-Migration Steps

### 1. Update Environment Configuration

Switch your application to use production database:

```bash
# In server/.env, comment out local URI and uncomment production URI
# MONGODB_URI=mongodb://localhost:27017/multi-vendor-ecommerce
MONGODB_URI=mongodb+srv://cojeh66312:<EMAIL>/multi-vendor-ecommerce?retryWrites=true&w=majority&appName=Cluster0Test
```

### 2. Test Application

Start your application and verify:
- User authentication works
- Products display correctly
- Orders can be placed
- All features function as expected

### 3. Monitor Performance

- Check Atlas monitoring dashboard
- Monitor query performance
- Set up alerts for database metrics

## Backup and Recovery

### Backup Files Location
- Local backups: `server/backups/`
- Each collection saved as separate JSON file
- Timestamped for easy identification

### Restore from Backup
If you need to restore from backup:

```bash
# This would require a custom restore script
# Contact support if restoration is needed
```

## Security Considerations

1. **Credentials**: Never commit production credentials to version control
2. **Network**: Use Atlas IP whitelist for security
3. **SSL**: Production connection uses SSL/TLS encryption
4. **Backup**: Store backups securely

## Support

If you encounter issues during migration:

1. Check the console output for specific error messages
2. Verify network connectivity to Atlas
3. Ensure sufficient disk space for backups
4. Check Atlas cluster status and quotas

## Migration Checklist

- [ ] Local MongoDB is running and accessible
- [ ] Production Atlas credentials are correct
- [ ] Network connection to Atlas is stable
- [ ] Sufficient disk space for backups
- [ ] Application is stopped during migration
- [ ] Backup created successfully
- [ ] Migration completed without errors
- [ ] Verification shows matching document counts
- [ ] Application tested with production database
- [ ] Environment variables updated for production

---

**Note**: This migration process is designed to be safe and reversible. Always test in a staging environment before migrating production data.