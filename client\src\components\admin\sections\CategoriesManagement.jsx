import React, { useState, useEffect } from 'react';
import {
  Tabs, Card, Button, Form, Input, message, Upload, Modal,
  Table, Switch, InputNumber, Select, Space, Popconfirm,
  Image, Tag, Tooltip
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, UploadOutlined,
  PictureOutlined, SettingOutlined, BankOutlined, AppstoreOutlined
} from '@ant-design/icons';
import { homepageSettingsApi, categoriesApi } from '../../../services/adminApi';
import DynamicCarouselModal from '../modals/DynamicCarouselModal';
import DynamicPromotionModal from '../modals/DynamicPromotionModal';
import dayjs from 'dayjs';

const { TabPane } = Tabs;
const { TextArea } = Input;
const { Option } = Select;


const HomepageSettings = () => {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('carousel');

  // Modal states
  const [carouselModalVisible, setCarouselModalVisible] = useState(false);
  const [promotionModalVisible, setPromotionModalVisible] = useState(false);
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);

  // Form states
  const [settingsForm] = Form.useForm();

  // Edit states
  const [editingCarousel, setEditingCarousel] = useState(null);
  const [editingPromotion, setEditingPromotion] = useState(null);

  // Featured Categories state
  const [allCategories, setAllCategories] = useState([]);
  const [categoriesLoading, setCategoriesLoading] = useState(false);

  useEffect(() => {
    fetchSettings();
    fetchAllCategories();
  }, []);

  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await homepageSettingsApi.getSettings();
      if (response.data.success) {
        setSettings(response.data.data);
        // Initialize settings form with current values
        settingsForm.setFieldsValue({
          autoPlayCarousel: response.data.data.settings?.autoPlayCarousel,
          carouselSpeed: response.data.data.settings?.carouselSpeed,
          showPromotions: response.data.data.settings?.showPromotions,
          maxCarouselImages: response.data.data.settings?.maxCarouselImages,
          maxPromotionImages: response.data.data.settings?.maxPromotionImages
        });
      }
    } catch (error) {
      message.error('Failed to fetch homepage settings');
    } finally {
      setLoading(false);
    }
  };

  const fetchAllCategories = async () => {
    setCategoriesLoading(true);
    try {
      const response = await categoriesApi.getAllCategories();
      setAllCategories(response.data.data.categories || []);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      message.error('Failed to fetch categories');
    } finally {
      setCategoriesLoading(false);
    }
  };

  // Carousel handlers
  const handleAddCarousel = () => {
    setEditingCarousel(null);
    setCarouselModalVisible(true);
  };

  const handleEditCarousel = () => {
    setEditingCarousel(settings.carouselImages || []);
    setCarouselModalVisible(true);
  };

  const handleDeleteCarousel = async (imageId) => {
    setLoading(true);
    try {
      await homepageSettingsApi.deleteCarouselImage(imageId);
      message.success('Carousel image deleted successfully');
      fetchSettings();
    } catch (error) {
      message.error('Failed to delete carousel image');
    } finally {
      setLoading(false);
    }
  };

  const handleCarouselSubmit = async (images) => {
    try {
      setLoading(true);

      if (editingCarousel) {
        // If editing, we need to clear existing images and add new ones
        // For simplicity, we'll delete all and re-add (in a real app, you might want to be more sophisticated)
        const existingImages = settings.carouselImages || [];

        // Delete all existing images
        for (const img of existingImages) {
          await homepageSettingsApi.deleteCarouselImage(img._id);
        }
      }

      // Add all new images
      for (const imageData of images) {
        await homepageSettingsApi.addCarouselImage(imageData);
      }

      message.success(`${images.length} carousel image(s) ${editingCarousel ? 'updated' : 'added'} successfully`);
      setCarouselModalVisible(false);
      fetchSettings();
    } catch (error) {
      console.error('Carousel save error:', error);
      message.error('Failed to save carousel images');
    } finally {
      setLoading(false);
    }
  };

  // Promotion handlers
  const handleAddPromotion = () => {
    setEditingPromotion(null);
    setPromotionModalVisible(true);
  };

  const handleEditPromotion = () => {
    setEditingPromotion(settings.promotionImages || []);
    setPromotionModalVisible(true);
  };

  const handleDeletePromotion = async (imageId) => {
    setLoading(true);
    try {
      await homepageSettingsApi.deletePromotionImage(imageId);
      message.success('Promotion image deleted successfully');
      fetchSettings();
    } catch (error) {
      message.error('Failed to delete promotion image');
    } finally {
      setLoading(false);
    }
  };

  const handlePromotionSubmit = async (images) => {
    try {
      setLoading(true);

      if (editingPromotion) {
        // If editing, we need to clear existing images and add new ones
        const existingImages = settings.promotionImages || [];

        // Delete all existing images
        for (const img of existingImages) {
          await homepageSettingsApi.deletePromotionImage(img._id);
        }
      }

      // Add all new images
      for (const imageData of images) {
        await homepageSettingsApi.addPromotionImage(imageData);
      }

      message.success(`${images.length} promotion image(s) ${editingPromotion ? 'updated' : 'added'} successfully`);
      setPromotionModalVisible(false);
      fetchSettings();
    } catch (error) {
      console.error('Promotion save error:', error);
      message.error('Failed to save promotion images');
    } finally {
      setLoading(false);
    }
  };

  // Settings handlers
  const handleSettingsSubmit = async () => {
    try {
      const values = await settingsForm.validateFields();
      setLoading(true);

      await homepageSettingsApi.updateGeneralSettings(values);
      message.success('Settings updated successfully');
      setSettingsModalVisible(false);
      fetchSettings();
    } catch (error) {
      message.error('Failed to update settings');
    } finally {
      setLoading(false);
    }
  };

  // Featured Category handlers
  const handleFeaturedCategoriesChange = (selectedCategoryIds) => {
    // Update the settings state with selected category IDs
    setSettings(prev => ({
      ...prev,
      featuredCategoryIds: selectedCategoryIds
    }));
  };

  const handleSaveFeaturedCategories = async () => {
    try {
      setLoading(true);
      const featuredCategoryIds = settings.featuredCategoryIds || [];

      await homepageSettingsApi.updateGeneralSettings({
        featuredCategoryIds: featuredCategoryIds
      });

      message.success('Featured categories updated successfully');
      fetchSettings();
    } catch (error) {
      console.error('Error saving featured categories:', error);
      message.error('Failed to save featured categories');
    } finally {
      setLoading(false);
    }
  };

  // Table columns for carousel images
  const carouselColumns = [
    {
      title: 'Image',
      dataIndex: 'imageUrl',
      key: 'imageUrl',
      width: 100,
      render: (imageUrl) => (
        <Image
          width={60}
          height={40}
          src={imageUrl}
          style={{ objectFit: 'cover', borderRadius: 4 }}
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
        />
      ),
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      render: (title) => title || '-',
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Sort Order',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      width: 100,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditCarousel(record)}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this carousel image?"
            onConfirm={() => handleDeleteCarousel(record._id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // Table columns for promotion images
  const promotionColumns = [
    {
      title: 'Image',
      dataIndex: 'imageUrl',
      key: 'imageUrl',
      width: 100,
      render: (imageUrl) => (
        <Image
          width={60}
          height={40}
          src={imageUrl}
          style={{ objectFit: 'cover', borderRadius: 4 }}
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
        />
      ),
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      render: (title) => title || '-',
    },
    {
      title: 'Position',
      dataIndex: 'position',
      key: 'position',
      render: (position) => (
        <Tag color="blue">{position}</Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Duration',
      key: 'duration',
      render: (_, record) => {
        if (record.startDate && record.endDate) {
          return (
            <div>
              <div>{dayjs(record.startDate).format('MMM DD, YYYY')}</div>
              <div>{dayjs(record.endDate).format('MMM DD, YYYY')}</div>
            </div>
          );
        }
        return '-';
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditPromotion(record)}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this promotion image?"
            onConfirm={() => handleDeletePromotion(record._id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];



  if (!settings) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          tabBarExtraContent={
            <Button
              type="primary"
              icon={<SettingOutlined />}
              onClick={() => setSettingsModalVisible(true)}
            >
              General Settings
            </Button>
          }
        >
          <TabPane tab={<span><PictureOutlined />Carousel Images</span>} key="carousel">
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddCarousel}
                >
                  Add Carousel Images
                </Button>
                {settings.carouselImages?.length > 0 && (
                  <Button
                    type="default"
                    icon={<EditOutlined />}
                    onClick={handleEditCarousel}
                  >
                    Edit All Images
                  </Button>
                )}
              </Space>
              <div style={{ marginTop: 8, color: '#666' }}>
                Manage up to 10 carousel images using Cloudinary links
              </div>
            </div>
            <Table
              columns={carouselColumns}
              dataSource={settings.carouselImages || []}
              rowKey="_id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab={<span><BankOutlined />Promotion Images</span>} key="promotions">
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddPromotion}
                >
                  Add Promotion Images
                </Button>
                {settings.promotionImages?.length > 0 && (
                  <Button
                    type="default"
                    icon={<EditOutlined />}
                    onClick={handleEditPromotion}
                  >
                    Edit All Images
                  </Button>
                )}
              </Space>
              <div style={{ marginTop: 8, color: '#666' }}>
                Manage up to 5 promotion images using Cloudinary links
              </div>
            </div>
            <Table
              columns={promotionColumns}
              dataSource={settings.promotionImages || []}
              rowKey="_id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab={<span><AppstoreOutlined />Featured Categories</span>} key="categories">
            <div style={{ marginBottom: 16 }}>
              <h4>Select up to 5 categories to display on homepage</h4>
              <p style={{ color: '#666', marginBottom: 16 }}>
                Choose which categories should appear in the homepage sidebar menu.
              </p>
            </div>

            <Form layout="vertical">
              <Form.Item label="Featured Categories">
                <Select
                  mode="multiple"
                  placeholder="Select categories (max 5)"
                  value={settings.featuredCategoryIds || []}
                  onChange={handleFeaturedCategoriesChange}
                  maxCount={5}
                  style={{ width: '100%' }}
                  loading={categoriesLoading}
                >
                  {allCategories.map(category => (
                    <Option key={category._id} value={category._id}>
                      {category.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Button
                type="primary"
                onClick={handleSaveFeaturedCategories}
                loading={loading}
              >
                Save Featured Categories
              </Button>
            </Form>
          </TabPane>
        </Tabs>
      </Card>

      {/* Dynamic Carousel Modal */}
      <DynamicCarouselModal
        visible={carouselModalVisible}
        onCancel={() => setCarouselModalVisible(false)}
        onSubmit={handleCarouselSubmit}
        loading={loading}
        editingImages={editingCarousel}
      />

      {/* Dynamic Promotion Modal */}
      <DynamicPromotionModal
        visible={promotionModalVisible}
        onCancel={() => setPromotionModalVisible(false)}
        onSubmit={handlePromotionSubmit}
        loading={loading}
        editingImages={editingPromotion}
      />

      {/* Settings Modal */}
      <Modal
        title="General Settings"
        open={settingsModalVisible}
        onOk={handleSettingsSubmit}
        onCancel={() => setSettingsModalVisible(false)}
        confirmLoading={loading}
        width={500}
      >
        <Form form={settingsForm} layout="vertical">
          <Form.Item name="autoPlayCarousel" label="Auto Play Carousel" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item name="carouselSpeed" label="Carousel Speed (ms)">
            <InputNumber min={1000} max={10000} step={500} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item name="showPromotions" label="Show Promotions" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item name="maxCarouselImages" label="Max Carousel Images">
            <InputNumber min={1} max={20} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item name="maxPromotionImages" label="Max Promotion Images">
            <InputNumber min={1} max={10} style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>


    </div>
  );
};

export default HomepageSettings;

