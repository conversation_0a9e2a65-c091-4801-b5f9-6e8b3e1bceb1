/**
 * Comprehensive End-to-End Tests for Order Status Flow
 * 
 * This test suite verifies that order status changes flow correctly from:
 * 1. Admin panel to vendor panel
 * 2. Vendor panel to admin panel  
 * 3. Admin/vendor panels to customer tracking pages
 * 4. All interfaces show consistent information
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { notification } from 'antd';

// Mock components and services
import OrdersManagement from '../components/vendor/sections/OrdersManagement';
import OrderTrackingPage from '../pages/OrderTrackingPage';
import { ordersApi } from '../services/adminApi';
import { vendorOrderApi } from '../services/vendorOrderApi';
import { orderTrackingApi } from '../services/orderTrackingApi';
import { io } from 'socket.io-client';

// Mock socket.io-client
vi.mock('socket.io-client', () => ({
  io: vi.fn(() => ({
    on: vi.fn(),
    emit: vi.fn(),
    disconnect: vi.fn()
  }))
}));

// Mock APIs
vi.mock('../services/adminApi', () => ({
  ordersApi: {
    getOrders: vi.fn(),
    getOrderStatistics: vi.fn(),
    updateOrderStatus: vi.fn()
  }
}));

vi.mock('../services/vendorOrderApi', () => ({
  vendorOrderApi: {
    getOrders: vi.fn(),
    getOrderAnalytics: vi.fn(),
    updateOrderStatus: vi.fn()
  }
}));

vi.mock('../services/orderTrackingApi', () => ({
  orderTrackingApi: {
    getTrackingByNumber: vi.fn(),
    getTrackingByOrderId: vi.fn()
  }
}));

// Mock auth hook
vi.mock('../hooks/useAuth', () => ({
  useAuth: () => ({
    user: { id: 'test-user-id', vendorId: 'test-vendor-id' },
    isAuthenticated: true
  })
}));

// Mock responsive hook
vi.mock('../hooks/useResponsive', () => ({
  default: () => ({
    isMobile: false,
    isTablet: false,
    isSmallScreen: false
  })
}));

// Test data
const mockOrder = {
  id: 'order-123',
  orderNumber: 'ORD-2024-001',
  status: 'pending',
  customer: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>'
  },
  items: [
    {
      id: 'item-1',
      product: { name: 'Test Product' },
      quantity: 2,
      price: 100,
      status: 'pending'
    }
  ],
  totalAmount: 200,
  createdAt: new Date().toISOString()
};

const mockStatistics = {
  totalOrders: 10,
  pendingOrders: 3,
  deliveredOrders: 5,
  totalRevenue: 2000
};

const mockTrackingData = {
  trackingNumber: 'TRK123456789',
  order: mockOrder,
  currentStatus: 'processing',
  timeline: [
    {
      status: 'order_confirmed',
      timestamp: new Date(),
      title: 'Order Confirmed',
      description: 'Your order has been confirmed'
    }
  ]
};

describe('Order Status Flow End-to-End Tests', () => {
  let mockSocket;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Mock socket instance
    mockSocket = {
      on: vi.fn(),
      emit: vi.fn(),
      disconnect: vi.fn()
    };
    io.mockReturnValue(mockSocket);

    // Mock API responses
    ordersApi.getOrders.mockResolvedValue({
      data: {
        success: true,
        data: { orders: [mockOrder], total: 1 }
      }
    });

    ordersApi.getOrderStatistics.mockResolvedValue({
      data: {
        success: true,
        data: mockStatistics
      }
    });

    vendorOrderApi.getOrders.mockResolvedValue({
      success: true,
      data: { orders: [mockOrder], total: 1 }
    });

    vendorOrderApi.getOrderAnalytics.mockResolvedValue({
      success: true,
      data: { stats: mockStatistics }
    });

    orderTrackingApi.getTrackingByNumber.mockResolvedValue({
      data: mockTrackingData
    });

    // Mock notification
    vi.spyOn(notification, 'success').mockImplementation(() => {});
    vi.spyOn(notification, 'info').mockImplementation(() => {});
    vi.spyOn(notification, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Admin Order Status Updates', () => {
    it('should update order status from admin panel and sync to vendor', async () => {
      // Mock successful status update
      ordersApi.updateOrderStatus.mockResolvedValue({
        data: {
          success: true,
          data: { order: { ...mockOrder, status: 'processing' } }
        }
      });

      // Render admin orders management
      render(
        <BrowserRouter>
          <OrdersManagement userType="admin" />
        </BrowserRouter>
      );

      // Wait for initial data load
      await waitFor(() => {
        expect(ordersApi.getOrders).toHaveBeenCalled();
        expect(ordersApi.getOrderStatistics).toHaveBeenCalled();
      });

      // Simulate status update
      const statusSelect = screen.getByDisplayValue('pending');
      fireEvent.change(statusSelect, { target: { value: 'processing' } });

      // Verify API call
      await waitFor(() => {
        expect(ordersApi.updateOrderStatus).toHaveBeenCalledWith(
          'order-123',
          'processing',
          'Status updated to processing'
        );
      });

      // Verify socket emission
      expect(mockSocket.emit).toHaveBeenCalledWith('orderStatusUpdate', {
        orderId: 'order-123',
        status: 'processing',
        updatedBy: 'test-user-id',
        userType: 'admin',
        orderNumber: 'ORD-2024-001',
        vendorId: null
      });

      // Verify success notification
      expect(notification.success).toHaveBeenCalledWith({
        message: 'Success',
        description: 'Order status updated to processing',
        placement: 'topRight'
      });
    });

    it('should receive real-time updates from vendor status changes', async () => {
      render(
        <BrowserRouter>
          <OrdersManagement userType="admin" />
        </BrowserRouter>
      );

      // Simulate receiving socket event from vendor
      const socketCallback = mockSocket.on.mock.calls.find(
        call => call[0] === 'orderStatusUpdated'
      )[1];

      socketCallback({
        orderId: 'order-123',
        status: 'shipped',
        updatedBy: 'vendor-user-id',
        updaterType: 'vendor',
        orderNumber: 'ORD-2024-001'
      });

      // Verify data refresh
      await waitFor(() => {
        expect(ordersApi.getOrders).toHaveBeenCalledTimes(2); // Initial + refresh
        expect(ordersApi.getOrderStatistics).toHaveBeenCalledTimes(2);
      });

      // Verify notification for external update
      expect(notification.info).toHaveBeenCalledWith({
        message: 'Order Status Updated',
        description: 'Order ORD-2024-001 status changed to shipped by vendor',
        placement: 'topRight'
      });
    });
  });

  describe('Vendor Order Status Updates', () => {
    it('should update order status from vendor panel and sync to admin', async () => {
      // Mock successful status update
      vendorOrderApi.updateOrderStatus.mockResolvedValue({
        success: true,
        data: { ...mockOrder, status: 'shipped' }
      });

      // Render vendor orders management
      render(
        <BrowserRouter>
          <OrdersManagement userType="vendor" />
        </BrowserRouter>
      );

      // Wait for initial data load
      await waitFor(() => {
        expect(vendorOrderApi.getOrders).toHaveBeenCalled();
        expect(vendorOrderApi.getOrderAnalytics).toHaveBeenCalled();
      });

      // Simulate status update
      const statusSelect = screen.getByDisplayValue('pending');
      fireEvent.change(statusSelect, { target: { value: 'shipped' } });

      // Verify API call
      await waitFor(() => {
        expect(vendorOrderApi.updateOrderStatus).toHaveBeenCalledWith(
          'order-123',
          {
            status: 'shipped',
            note: 'Status updated to shipped'
          }
        );
      });

      // Verify socket emission
      expect(mockSocket.emit).toHaveBeenCalledWith('orderStatusUpdate', {
        orderId: 'order-123',
        status: 'shipped',
        updatedBy: 'test-user-id',
        userType: 'vendor',
        orderNumber: 'ORD-2024-001',
        vendorId: 'test-vendor-id'
      });
    });
  });

  describe('Customer Order Tracking Integration', () => {
    it('should display updated order status in customer tracking', async () => {
      // Render order tracking page
      render(
        <BrowserRouter>
          <OrderTrackingPage />
        </BrowserRouter>
      );

      // Simulate tracking search
      const searchInput = screen.getByPlaceholderText(/enter tracking number/i);
      const searchButton = screen.getByText('Track');

      fireEvent.change(searchInput, { target: { value: 'TRK123456789' } });
      fireEvent.click(searchButton);

      // Verify API call
      await waitFor(() => {
        expect(orderTrackingApi.getTrackingByNumber).toHaveBeenCalledWith('TRK123456789');
      });

      // Verify tracking data display
      expect(screen.getByText('TRK123456789')).toBeInTheDocument();
      expect(screen.getByText('PROCESSING')).toBeInTheDocument();
    });

    it('should refresh tracking data when refresh button is clicked', async () => {
      render(
        <BrowserRouter>
          <OrderTrackingPage />
        </BrowserRouter>
      );

      // Simulate tracking search first
      const searchInput = screen.getByPlaceholderText(/enter tracking number/i);
      fireEvent.change(searchInput, { target: { value: 'TRK123456789' } });
      fireEvent.click(screen.getByText('Track'));

      await waitFor(() => {
        expect(orderTrackingApi.getTrackingByNumber).toHaveBeenCalledTimes(1);
      });

      // Click refresh button
      const refreshButton = screen.getByTitle('Refresh tracking data');
      fireEvent.click(refreshButton);

      // Verify API called again
      await waitFor(() => {
        expect(orderTrackingApi.getTrackingByNumber).toHaveBeenCalledTimes(2);
      });

      // Verify refresh notification
      expect(notification.success).toHaveBeenCalledWith({
        message: 'Refreshed',
        description: 'Order tracking data has been updated',
        placement: 'topRight'
      });
    });
  });

  describe('Data Consistency Across Interfaces', () => {
    it('should maintain consistent order status across admin and vendor panels', async () => {
      // Test that both admin and vendor see the same order status
      const updatedOrder = { ...mockOrder, status: 'delivered' };
      
      ordersApi.getOrders.mockResolvedValue({
        data: {
          success: true,
          data: { orders: [updatedOrder], total: 1 }
        }
      });

      vendorOrderApi.getOrders.mockResolvedValue({
        success: true,
        data: { orders: [updatedOrder], total: 1 }
      });

      // Render both admin and vendor components
      const { rerender } = render(
        <BrowserRouter>
          <OrdersManagement userType="admin" />
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(screen.getByText('delivered')).toBeInTheDocument();
      });

      rerender(
        <BrowserRouter>
          <OrdersManagement userType="vendor" />
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(screen.getByText('delivered')).toBeInTheDocument();
      });
    });

    it('should show consistent statistics after status updates', async () => {
      const updatedStats = {
        ...mockStatistics,
        pendingOrders: 2,
        deliveredOrders: 6
      };

      ordersApi.getOrderStatistics.mockResolvedValue({
        data: {
          success: true,
          data: updatedStats
        }
      });

      render(
        <BrowserRouter>
          <OrdersManagement userType="admin" />
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(screen.getByText('2')).toBeInTheDocument(); // Pending orders
        expect(screen.getByText('6')).toBeInTheDocument(); // Delivered orders
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      ordersApi.updateOrderStatus.mockRejectedValue(new Error('Network error'));

      render(
        <BrowserRouter>
          <OrdersManagement userType="admin" />
        </BrowserRouter>
      );

      // Simulate status update that fails
      const statusSelect = screen.getByDisplayValue('pending');
      fireEvent.change(statusSelect, { target: { value: 'processing' } });

      await waitFor(() => {
        expect(notification.error).toHaveBeenCalledWith({
          message: 'Error',
          description: 'Network error'
        });
      });
    });

    it('should handle socket connection errors', async () => {
      // Mock socket connection failure
      io.mockImplementation(() => {
        throw new Error('Socket connection failed');
      });

      // Should not crash the component
      expect(() => {
        render(
          <BrowserRouter>
            <OrdersManagement userType="admin" />
          </BrowserRouter>
        );
      }).not.toThrow();
    });
  });
});
