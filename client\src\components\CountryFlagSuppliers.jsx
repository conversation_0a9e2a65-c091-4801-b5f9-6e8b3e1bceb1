import React, { useState } from 'react';

const CountryFlagSuppliers = () => {
    // Comprehensive list of all countries with flag codes and names
    const countries = [
        { code: 'AD', name: 'Andorra' },
        { code: 'AE', name: 'United Arab Emirates' },
        { code: 'AF', name: 'Afghanistan' },
        { code: 'AG', name: 'Antigua and Barbuda' },
        { code: 'AI', name: 'Anguilla' },
        { code: 'AL', name: 'Albania' },
        { code: 'AM', name: 'Armenia' },
        { code: 'AO', name: 'Angola' },
        { code: 'AQ', name: 'Antarctica' },
        { code: 'AR', name: 'Argentina' },
        { code: 'AS', name: 'American Samoa' },
        { code: 'AT', name: 'Austria' },
        { code: 'AU', name: 'Australia' },
        { code: 'AW', name: 'Aruba' },
        { code: 'AX', name: 'Åland Islands' },
        { code: 'AZ', name: 'Azerbaijan' },
        { code: 'BA', name: 'Bosnia and Herzegovina' },
        { code: 'BB', name: 'Barbados' },
        { code: 'BD', name: 'Bangladesh' },
        { code: 'BE', name: 'Belgium' },
        { code: 'BF', name: 'Burkina Faso' },
        { code: 'BG', name: 'Bulgaria' },
        { code: 'BH', name: 'Bahrain' },
        { code: 'BI', name: 'Burundi' },
        { code: 'BJ', name: 'Benin' },
        { code: 'BL', name: 'Saint Barthélemy' },
        { code: 'BM', name: 'Bermuda' },
        { code: 'BN', name: 'Brunei' },
        { code: 'BO', name: 'Bolivia' },
        { code: 'BQ', name: 'Caribbean Netherlands' },
        { code: 'BR', name: 'Brazil' },
        { code: 'BS', name: 'Bahamas' },
        { code: 'BT', name: 'Bhutan' },
        { code: 'BV', name: 'Bouvet Island' },
        { code: 'BW', name: 'Botswana' },
        { code: 'BY', name: 'Belarus' },
        { code: 'BZ', name: 'Belize' },
        { code: 'CA', name: 'Canada' },
        { code: 'CC', name: 'Cocos Islands' },
        { code: 'CD', name: 'DR Congo' },
        { code: 'CF', name: 'Central African Republic' },
        { code: 'CG', name: 'Republic of the Congo' },
        { code: 'CH', name: 'Switzerland' },
        { code: 'CI', name: 'Côte d\'Ivoire' },
        { code: 'CK', name: 'Cook Islands' },
        { code: 'CL', name: 'Chile' },
        { code: 'CM', name: 'Cameroon' },
        { code: 'CN', name: 'China' },
        { code: 'CO', name: 'Colombia' },
        { code: 'CR', name: 'Costa Rica' },
        { code: 'CU', name: 'Cuba' },
        { code: 'CV', name: 'Cape Verde' },
        { code: 'CW', name: 'Curaçao' },
        { code: 'CX', name: 'Christmas Island' },
        { code: 'CY', name: 'Cyprus' },
        { code: 'CZ', name: 'Czech Republic' },
        { code: 'DE', name: 'Germany' },
        { code: 'DJ', name: 'Djibouti' },
        { code: 'DK', name: 'Denmark' },
        { code: 'DM', name: 'Dominica' },
        { code: 'DO', name: 'Dominican Republic' },
        { code: 'DZ', name: 'Algeria' },
        { code: 'EC', name: 'Ecuador' },
        { code: 'EE', name: 'Estonia' },
        { code: 'EG', name: 'Egypt' },
        { code: 'EH', name: 'Western Sahara' },
        { code: 'ER', name: 'Eritrea' },
        { code: 'ES', name: 'Spain' },
        { code: 'ET', name: 'Ethiopia' },
        { code: 'FI', name: 'Finland' },
        { code: 'FJ', name: 'Fiji' },
        { code: 'FK', name: 'Falkland Islands' },
        { code: 'FM', name: 'Micronesia' },
        { code: 'FO', name: 'Faroe Islands' },
        { code: 'FR', name: 'France' },
        { code: 'GA', name: 'Gabon' },
        { code: 'GB', name: 'United Kingdom' },
        { code: 'GD', name: 'Grenada' },
        { code: 'GE', name: 'Georgia' },
        { code: 'GF', name: 'French Guiana' },
        { code: 'GG', name: 'Guernsey' },
        { code: 'GH', name: 'Ghana' },
        { code: 'GI', name: 'Gibraltar' },
        { code: 'GL', name: 'Greenland' },
        { code: 'GM', name: 'Gambia' },
        { code: 'GN', name: 'Guinea' },
        { code: 'GP', name: 'Guadeloupe' },
        { code: 'GQ', name: 'Equatorial Guinea' },
        { code: 'GR', name: 'Greece' },
        { code: 'GS', name: 'South Georgia' },
        { code: 'GT', name: 'Guatemala' },
        { code: 'GU', name: 'Guam' },
        { code: 'GW', name: 'Guinea-Bissau' },
        { code: 'GY', name: 'Guyana' },
        { code: 'HK', name: 'Hong Kong' },
        { code: 'HM', name: 'Heard Island' },
        { code: 'HN', name: 'Honduras' },
        { code: 'HR', name: 'Croatia' },
        { code: 'HT', name: 'Haiti' },
        { code: 'HU', name: 'Hungary' },
        { code: 'ID', name: 'Indonesia' },
        { code: 'IE', name: 'Ireland' },
        { code: 'IL', name: 'Israel' },
        { code: 'IM', name: 'Isle of Man' },
        { code: 'IN', name: 'India' },
        { code: 'IO', name: 'British Indian Ocean Territory' },
        { code: 'IQ', name: 'Iraq' },
        { code: 'IR', name: 'Iran' },
        { code: 'IS', name: 'Iceland' },
        { code: 'IT', name: 'Italy' },
        { code: 'JE', name: 'Jersey' },
        { code: 'JM', name: 'Jamaica' },
        { code: 'JO', name: 'Jordan' },
        { code: 'JP', name: 'Japan' },
        { code: 'KE', name: 'Kenya' },
        { code: 'KG', name: 'Kyrgyzstan' },
        { code: 'KH', name: 'Cambodia' },
        { code: 'KI', name: 'Kiribati' },
        { code: 'KM', name: 'Comoros' },
        { code: 'KN', name: 'Saint Kitts and Nevis' },
        { code: 'KP', name: 'North Korea' },
        { code: 'KR', name: 'South Korea' },
        { code: 'KW', name: 'Kuwait' },
        { code: 'KY', name: 'Cayman Islands' },
        { code: 'KZ', name: 'Kazakhstan' },
        { code: 'LA', name: 'Laos' },
        { code: 'LB', name: 'Lebanon' },
        { code: 'LC', name: 'Saint Lucia' },
        { code: 'LI', name: 'Liechtenstein' },
        { code: 'LK', name: 'Sri Lanka' },
        { code: 'LR', name: 'Liberia' },
        { code: 'LS', name: 'Lesotho' },
        { code: 'LT', name: 'Lithuania' },
        { code: 'LU', name: 'Luxembourg' },
        { code: 'LV', name: 'Latvia' },
        { code: 'LY', name: 'Libya' },
        { code: 'MA', name: 'Morocco' },
        { code: 'MC', name: 'Monaco' },
        { code: 'MD', name: 'Moldova' },
        { code: 'ME', name: 'Montenegro' },
        { code: 'MF', name: 'Saint Martin' },
        { code: 'MG', name: 'Madagascar' },
        { code: 'MH', name: 'Marshall Islands' },
        { code: 'MK', name: 'North Macedonia' },
        { code: 'ML', name: 'Mali' },
        { code: 'MM', name: 'Myanmar' },
        { code: 'MN', name: 'Mongolia' },
        { code: 'MO', name: 'Macao' },
        { code: 'MP', name: 'Northern Mariana Islands' },
        { code: 'MQ', name: 'Martinique' },
        { code: 'MR', name: 'Mauritania' },
        { code: 'MS', name: 'Montserrat' },
        { code: 'MT', name: 'Malta' },
        { code: 'MU', name: 'Mauritius' },
        { code: 'MV', name: 'Maldives' },
        { code: 'MW', name: 'Malawi' },
        { code: 'MX', name: 'Mexico' },
        { code: 'MY', name: 'Malaysia' },
        { code: 'MZ', name: 'Mozambique' },
        { code: 'NA', name: 'Namibia' },
        { code: 'NC', name: 'New Caledonia' },
        { code: 'NE', name: 'Niger' },
        { code: 'NF', name: 'Norfolk Island' },
        { code: 'NG', name: 'Nigeria' },
        { code: 'NI', name: 'Nicaragua' },
        { code: 'NL', name: 'Netherlands' },
        { code: 'NO', name: 'Norway' },
        { code: 'NP', name: 'Nepal' },
        { code: 'NR', name: 'Nauru' },
        { code: 'NU', name: 'Niue' },
        { code: 'NZ', name: 'New Zealand' },
        { code: 'OM', name: 'Oman' },
        { code: 'PA', name: 'Panama' },
        { code: 'PE', name: 'Peru' },
        { code: 'PF', name: 'French Polynesia' },
        { code: 'PG', name: 'Papua New Guinea' },
        { code: 'PH', name: 'Philippines' },
        { code: 'PK', name: 'Pakistan' },
        { code: 'PL', name: 'Poland' },
        { code: 'PM', name: 'Saint Pierre and Miquelon' },
        { code: 'PN', name: 'Pitcairn Islands' },
        { code: 'PR', name: 'Puerto Rico' },
        { code: 'PS', name: 'Palestine' },
        { code: 'PT', name: 'Portugal' },
        { code: 'PW', name: 'Palau' },
        { code: 'PY', name: 'Paraguay' },
        { code: 'QA', name: 'Qatar' },
        { code: 'RE', name: 'Réunion' },
        { code: 'RO', name: 'Romania' },
        { code: 'RS', name: 'Serbia' },
        { code: 'RU', name: 'Russia' },
        { code: 'RW', name: 'Rwanda' },
        { code: 'SA', name: 'Saudi Arabia' },
        { code: 'SB', name: 'Solomon Islands' },
        { code: 'SC', name: 'Seychelles' },
        { code: 'SD', name: 'Sudan' },
        { code: 'SE', name: 'Sweden' },
        { code: 'SG', name: 'Singapore' },
        { code: 'SH', name: 'Saint Helena' },
        { code: 'SI', name: 'Slovenia' },
        { code: 'SJ', name: 'Svalbard and Jan Mayen' },
        { code: 'SK', name: 'Slovakia' },
        { code: 'SL', name: 'Sierra Leone' },
        { code: 'SM', name: 'San Marino' },
        { code: 'SN', name: 'Senegal' },
        { code: 'SO', name: 'Somalia' },
        { code: 'SR', name: 'Suriname' },
        { code: 'SS', name: 'South Sudan' },
        { code: 'ST', name: 'São Tomé and Príncipe' },
        { code: 'SV', name: 'El Salvador' },
        { code: 'SX', name: 'Sint Maarten' },
        { code: 'SY', name: 'Syria' },
        { code: 'SZ', name: 'Eswatini' },
        { code: 'TC', name: 'Turks and Caicos Islands' },
        { code: 'TD', name: 'Chad' },
        { code: 'TF', name: 'French Southern Territories' },
        { code: 'TG', name: 'Togo' },
        { code: 'TH', name: 'Thailand' },
        { code: 'TJ', name: 'Tajikistan' },
        { code: 'TK', name: 'Tokelau' },
        { code: 'TL', name: 'Timor-Leste' },
        { code: 'TM', name: 'Turkmenistan' },
        { code: 'TN', name: 'Tunisia' },
        { code: 'TO', name: 'Tonga' },
        { code: 'TR', name: 'Türkiye' },
        { code: 'TT', name: 'Trinidad and Tobago' },
        { code: 'TV', name: 'Tuvalu' },
        { code: 'TW', name: 'Taiwan' },
        { code: 'TZ', name: 'Tanzania' },
        { code: 'UA', name: 'Ukraine' },
        { code: 'UG', name: 'Uganda' },
        { code: 'UM', name: 'U.S. Minor Outlying Islands' },
        { code: 'US', name: 'United States' },
        { code: 'UY', name: 'Uruguay' },
        { code: 'UZ', name: 'Uzbekistan' },
        { code: 'VA', name: 'Vatican City' },
        { code: 'VC', name: 'Saint Vincent and the Grenadines' },
        { code: 'VE', name: 'Venezuela' },
        { code: 'VG', name: 'British Virgin Islands' },
        { code: 'VI', name: 'U.S. Virgin Islands' },
        { code: 'VN', name: 'Vietnam' },
        { code: 'VU', name: 'Vanuatu' },
        { code: 'WF', name: 'Wallis and Futuna' },
        { code: 'WS', name: 'Samoa' },
        { code: 'XK', name: 'Kosovo' },
        { code: 'YE', name: 'Yemen' },
        { code: 'YT', name: 'Mayotte' },
        { code: 'ZA', name: 'South Africa' },
        { code: 'ZM', name: 'Zambia' },
        { code: 'ZW', name: 'Zimbabwe' }
    ];

    const [visibleCount, setVisibleCount] = useState(8);
    const itemsPerPage = 8;

    const handleViewMore = () => {
        setVisibleCount(prevCount => prevCount + itemsPerPage);
    };

    const visibleCountries = countries.slice(0, visibleCount);
    const hasMore = visibleCount < countries.length;

    return (
        <div className="w-full max-w-6xl mx-auto p-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">Find suppliers by country or region</h2>
            
            {/* Flag Grid */}
            <div className="grid grid-cols-4 md:grid-cols-8 gap-4 mb-8">
                {visibleCountries.map((country) => (
                    <div 
                        key={country.code}
                        className="flex flex-col items-center p-4 bg-white rounded-lg border border-gray-200 hover:border-orange-300 hover:shadow-md transition-all duration-200 cursor-pointer group"
                    >
                        <div className="mb-3 overflow-hidden rounded">
                            <img 
                                src={`https://flagcdn.com/w80/${country.code.toLowerCase()}.png`}
                                alt={`${country.name} flag`}
                                className="w-12 h-8 object-cover group-hover:scale-105 transition-transform duration-200"
                                onError={(e) => {
                                    // Fallback in case flag image fails to load
                                    e.target.src = `https://flagcdn.com/w40/${country.code.toLowerCase()}.png`;
                                }}
                            />
                        </div>
                        <span className="text-sm font-medium text-gray-700 text-center leading-tight">
                            {country.name}
                        </span>
                    </div>
                ))}
            </div>

            {/* View More Button */}
            {hasMore && (
                <div className="text-center">
                    <button 
                        onClick={handleViewMore}
                        className="bg-orange-500 text-white px-6 py-2 rounded-3xl text-sm font-medium hover:bg-orange-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
                    >
                        View More ({countries.length - visibleCount} remaining)
                    </button>
                </div>
            )}

            {/* Show total count when all items are visible */}
            {!hasMore && countries.length > itemsPerPage && (
                <div className="text-center text-gray-500 text-sm">
                    Showing all {countries.length} countries
                </div>
            )}
        </div>
    );
};

export default CountryFlagSuppliers;
