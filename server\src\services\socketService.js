const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const { User, Vendor } = require('../models');
const orderSocket = require('../socket/orderSocket');

class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map();
    this.adminSockets = new Set();
  }

  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: [
          'https://multi-vendor-ecommerce-seven.vercel.app',
          'https://multi-vendor-server-1tb9.onrender.com',
          'http://localhost:3000',
          'http://localhost:3001',
          'http://localhost:5173',
          'http://localhost:4173',
          'http://127.0.0.1:3000',
          'http://127.0.0.1:5173',
          'http://127.0.0.1:4173'
        ],
        credentials: true
      }
    });

    this.setupMiddleware();
    this.setupEventHandlers();
    
    console.log('🔌 Socket.IO service initialized');
  }

  setupMiddleware() {
    // Authentication middleware
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.id).select('-password');
        
        if (!user) {
          return next(new Error('User not found'));
        }

        socket.userId = user._id.toString();
        socket.userRole = user.role;
        socket.user = user;
        
        next();
      } catch (error) {
        next(new Error('Invalid authentication token'));
      }
    });
  }

  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`User connected: ${socket.user.email} (${socket.userRole})`);
      
      // Store connected user
      this.connectedUsers.set(socket.userId, {
        socketId: socket.id,
        user: socket.user,
        connectedAt: new Date()
      });

      // Add to admin sockets if user is admin
      if (socket.userRole === 'admin') {
        this.adminSockets.add(socket.id);
        socket.join('admin-room');
        
        // Send current stats to newly connected admin
        this.sendDashboardUpdate(socket.id);
      }

      // Join user-specific room
      socket.join(`user-${socket.userId}`);

      // Handle admin-specific events
      if (socket.userRole === 'admin') {
        this.setupAdminEvents(socket);
        this.setupOrderEvents(socket, 'admin');
      }

      // Handle vendor-specific events
      if (socket.userRole === 'vendor') {
        this.setupVendorEvents(socket);
        this.setupOrderEvents(socket, 'vendor');
      }

      // Handle customer-specific events
      if (socket.userRole === 'customer') {
        this.setupCustomerEvents(socket);
      }

      // Handle disconnection
      socket.on('disconnect', () => {
        console.log(`User disconnected: ${socket.user.email}`);
        this.connectedUsers.delete(socket.userId);
        this.adminSockets.delete(socket.id);
      });

      // Handle ping/pong for connection health
      socket.on('ping', () => {
        socket.emit('pong');
      });
    });
  }

  setupAdminEvents(socket) {
    // Request real-time dashboard data
    socket.on('admin:request-dashboard', () => {
      this.sendDashboardUpdate(socket.id);
    });

    // Subscribe to specific data updates
    socket.on('admin:subscribe', (dataTypes) => {
      if (Array.isArray(dataTypes)) {
        dataTypes.forEach(type => {
          socket.join(`admin-${type}`);
        });
      }
    });

    // Unsubscribe from data updates
    socket.on('admin:unsubscribe', (dataTypes) => {
      if (Array.isArray(dataTypes)) {
        dataTypes.forEach(type => {
          socket.leave(`admin-${type}`);
        });
      }
    });
  }

  setupVendorEvents(socket) {
    // Join vendor-specific room
    socket.join(`vendor-${socket.userId}`);

    // Handle vendor-specific subscriptions
    socket.on('vendor:subscribe-orders', () => {
      socket.join(`vendor-orders-${socket.userId}`);
    });

    socket.on('vendor:subscribe-products', () => {
      socket.join(`vendor-products-${socket.userId}`);
    });
  }

  setupCustomerEvents(socket) {
    // Handle customer-specific subscriptions
    socket.on('customer:subscribe-orders', () => {
      socket.join(`customer-orders-${socket.userId}`);
    });
  }

  // Admin notification methods
  async sendDashboardUpdate(socketId = null) {
    try {
      const { User, Vendor, Product, Order } = require('../models');
      
      // Get real-time metrics
      const now = new Date();
      const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      
      const [
        todayOrders,
        todayRevenue,
        todayUsers,
        activeUsers,
        pendingOrders,
        lowStockProducts
      ] = await Promise.all([
        Order.countDocuments({ createdAt: { $gte: startOfToday } }),
        Order.aggregate([
          { $match: { createdAt: { $gte: startOfToday } } },
          { $group: { _id: null, total: { $sum: '$pricing.total' } } }
        ]),
        User.countDocuments({ createdAt: { $gte: startOfToday } }),
        this.connectedUsers.size,
        Order.countDocuments({ status: 'pending' }),
        Product.countDocuments({
          'inventory.trackQuantity': true,
          $expr: { $lte: ['$inventory.quantity', '$inventory.lowStockThreshold'] }
        })
      ]);

      const dashboardData = {
        realTime: {
          todayOrders,
          todayRevenue: todayRevenue[0]?.total || 0,
          todayUsers,
          activeUsers,
          pendingOrders,
          lowStockProducts,
          timestamp: now
        }
      };

      if (socketId) {
        this.io.to(socketId).emit('admin:dashboard-update', dashboardData);
      } else {
        this.io.to('admin-room').emit('admin:dashboard-update', dashboardData);
      }
    } catch (error) {
      console.error('Error sending dashboard update:', error);
    }
  }

  // Notification methods for different events
  notifyNewOrder(order) {
    // Notify all admins
    this.io.to('admin-room').emit('admin:new-order', {
      type: 'new_order',
      data: order,
      timestamp: new Date()
    });

    // Notify specific vendor
    if (order.items && order.items.length > 0) {
      order.items.forEach(item => {
        if (item.vendor) {
          this.io.to(`vendor-${item.vendor}`).emit('vendor:new-order', {
            type: 'new_order',
            data: order,
            timestamp: new Date()
          });
        }
      });
    }

    // Notify customer
    this.io.to(`customer-orders-${order.customer}`).emit('customer:order-update', {
      type: 'order_created',
      data: order,
      timestamp: new Date()
    });

    // Update dashboard
    this.sendDashboardUpdate();
  }

  notifyOrderStatusUpdate(order, oldStatus, newStatus) {
    // Notify all admins
    this.io.to('admin-room').emit('admin:order-status-update', {
      type: 'order_status_update',
      data: { order, oldStatus, newStatus },
      timestamp: new Date()
    });

    // Notify customer
    this.io.to(`customer-orders-${order.customer}`).emit('customer:order-update', {
      type: 'status_update',
      data: { order, oldStatus, newStatus },
      timestamp: new Date()
    });

    // Notify vendors
    if (order.items && order.items.length > 0) {
      order.items.forEach(item => {
        if (item.vendor) {
          this.io.to(`vendor-orders-${item.vendor}`).emit('vendor:order-update', {
            type: 'status_update',
            data: { order, oldStatus, newStatus },
            timestamp: new Date()
          });
        }
      });
    }
  }

  notifyNewUser(user) {
    this.io.to('admin-room').emit('admin:new-user', {
      type: 'new_user',
      data: user,
      timestamp: new Date()
    });

    this.sendDashboardUpdate();
  }

  notifyNewVendor(vendor) {
    this.io.to('admin-room').emit('admin:new-vendor', {
      type: 'new_vendor',
      data: vendor,
      timestamp: new Date()
    });

    this.sendDashboardUpdate();
  }

  notifyLowStock(product) {
    this.io.to('admin-room').emit('admin:low-stock-alert', {
      type: 'low_stock',
      data: product,
      timestamp: new Date()
    });

    // Notify the vendor
    this.io.to(`vendor-${product.vendor}`).emit('vendor:low-stock-alert', {
      type: 'low_stock',
      data: product,
      timestamp: new Date()
    });
  }

  notifyPaymentUpdate(order, paymentData) {
    this.io.to('admin-room').emit('admin:payment-update', {
      type: 'payment_update',
      data: { order, payment: paymentData },
      timestamp: new Date()
    });

    this.io.to(`customer-orders-${order.customer}`).emit('customer:payment-update', {
      type: 'payment_update',
      data: { order, payment: paymentData },
      timestamp: new Date()
    });
  }

  // Setup order-related socket events
  setupOrderEvents(socket, userType) {
    // Handle order status updates
    socket.on('orderStatusUpdate', async (data) => {
      try {
        const { orderId, status, updatedBy, userType: updaterType } = data;
        
        console.log(`Order ${orderId} status updated to ${status} by ${updaterType} ${updatedBy}`);

        // Broadcast to admins
        socket.to('admin-room').emit('orderStatusUpdated', {
          orderId,
          status,
          updatedBy,
          updaterType,
          timestamp: new Date(),
          orderNumber: data.orderNumber
        });

        // If updated by admin, notify relevant vendor
        if (updaterType === 'admin' && data.vendorId) {
          socket.to(`vendor-${data.vendorId}`).emit('orderStatusUpdated', {
            orderId,
            status,
            updatedBy,
            updaterType,
            timestamp: new Date(),
            orderNumber: data.orderNumber
          });
        }

        // If updated by vendor, notify admins
        if (updaterType === 'vendor') {
          socket.to('admin-room').emit('orderStatusUpdated', {
            orderId,
            status,
            updatedBy,
            updaterType,
            timestamp: new Date(),
            orderNumber: data.orderNumber
          });
        }

      } catch (error) {
        console.error('Error handling order status update:', error);
        socket.emit('error', { message: 'Failed to broadcast order update' });
      }
    });

    // Handle new order notifications
    socket.on('newOrder', async (data) => {
      try {
        const { orderId, orderNumber, vendorId, customerId, totalAmount } = data;
        
        console.log(`New order ${orderNumber} created`);

        // Notify admins
        socket.to('admin-room').emit('newOrder', {
          orderId,
          orderNumber,
          vendorId,
          customerId,
          totalAmount,
          timestamp: new Date()
        });

        // Notify relevant vendor
        if (vendorId) {
          socket.to(`vendor-${vendorId}`).emit('newOrder', {
            orderId,
            orderNumber,
            vendorId,
            customerId,
            totalAmount,
            timestamp: new Date()
          });
        }

      } catch (error) {
        console.error('Error handling new order notification:', error);
        socket.emit('error', { message: 'Failed to broadcast new order' });
      }
    });
  }

  // Utility methods
  getConnectedUsers() {
    return Array.from(this.connectedUsers.values());
  }

  getConnectedAdmins() {
    return Array.from(this.connectedUsers.values()).filter(user => user.user.role === 'admin');
  }

  isUserOnline(userId) {
    return this.connectedUsers.has(userId.toString());
  }

  sendToUser(userId, event, data) {
    this.io.to(`user-${userId}`).emit(event, data);
  }

  sendToAdmins(event, data) {
    this.io.to('admin-room').emit(event, data);
  }

  broadcastToAll(event, data) {
    this.io.emit(event, data);
  }
}

// Create singleton instance
const socketService = new SocketService();

module.exports = socketService;