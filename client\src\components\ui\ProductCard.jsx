import React, { useEffect, useState } from 'react';
import { Pagination, message, notification } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import { productsApi } from '../../services/publicApi';
import { getFallbackImageUrl, getPrimaryProductImage } from '../../utils/imageUtils';
import { useCart } from '../../contexts/CartContext';
import { useAuth } from '../../hooks/useAuth';

const ProductCard = ({ initialPage = 1, showFeaturedOnly = false, limit = 20 }) => {
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [currentPage, setCurrentPage] = useState(initialPage);
    const [totalProducts, setTotalProducts] = useState(0);
    const [pageSize] = useState(limit);
    const navigate = useNavigate();
    const location = useLocation();
    const { addToCart, loading: cartLoading } = useCart();
    const { isAuthenticated } = useAuth();

    useEffect(() => {
        const fetchProducts = async () => {
            setLoading(true);
            try {
                let response;

                if (showFeaturedOnly) {
                    response = await productsApi.getFeaturedProducts(pageSize);
                } else {
                    const params = {
                        page: currentPage,
                        limit: pageSize,
                        status: 'active',
                        visibility: 'public'
                    };
                    response = await productsApi.getProducts(params);
                }

                if (response.data.success) {
                    const products = response.data.data.products || response.data.data;

                    // Transform products to match expected format
                    const transformedProducts = products.map(product => ({
                        id: product._id,
                        title: product.name,
                        price: product.pricing?.salePrice || product.pricing?.basePrice || 0,
                        images: product.images || [],
                        category: product.category,
                        vendor: product.vendor,
                        rating: product.reviews?.averageRating || 0,
                        reviewCount: product.reviews?.totalReviews || 0,
                        inStock: product.inventory?.quantity > 0,
                        stockCount: product.inventory?.quantity || 0
                    }));

                    setProducts(transformedProducts);

                    // Set total products from pagination info
                    const pagination = response.data.data.pagination;
                    if (pagination) {
                        setTotalProducts(pagination.totalProducts);
                    } else {
                        setTotalProducts(transformedProducts.length);
                    }

                    if (transformedProducts.length === 0) {
                        message.info('No products found. Please add some products from the vendor dashboard.');
                    }
                } else {
                    throw new Error('Failed to fetch products');
                }
            } catch (err) {
                console.error('Error fetching products:', err);
                setError('Failed to load products. Please try again later.');
                message.error('Failed to load products');
                setProducts([]);
                setTotalProducts(0);
            } finally {
                setLoading(false);
            }
        };

        fetchProducts();
    }, [currentPage, pageSize, showFeaturedOnly]);

    // Update current page when initialPage prop changes
    useEffect(() => {
        setCurrentPage(initialPage);
    }, [initialPage]);

    if (loading) {
        // Simple skeleton placeholders
        return (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6 p-4">
                {Array.from({ length: 10 }).map((_, i) => (
                    <div key={i} className="animate-pulse bg-gray-200 h-80 rounded-lg" />
                ))}
            </div>
        );
    }

    if (error) {
        return <div className="text-red-500 p-4">{error}</div>;
    }

    if (!products.length && !loading) {
        return (
            <div className="p-8 text-center">
                <div className="text-gray-500 mb-4">
                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                    </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                <p className="text-gray-500">
                    {showFeaturedOnly
                        ? "No featured products available at the moment."
                        : "No products are currently available. Vendors can add products from their dashboard."
                    }
                </p>
            </div>
        );
    }

    // Products are already paginated from the API, so we display them directly
    const displayedProducts = products;

    const handlePageChange = (page) => {
        setCurrentPage(page);

        // If we're on the home page and user clicks page 2 or higher, navigate to /page route
        if (location.pathname === '/' && page > 1) {
            navigate(`/page?q=${page}`);
        }
        // If we're on /page route, update the URL
        else if (location.pathname === '/page') {
            navigate(`/page?q=${page}`);
        }

        // Scroll to top when page changes
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const handleProductClick = (productId) => {
        navigate(`/product/${productId}`);
    };

    const handleCartClick = async (e, productId) => {
        e.stopPropagation();
        
        // Check if user is logged in
        if (!isAuthenticated) {
            notification.warning({
                message: 'Login Required',
                description: 'Please log in to add items to your cart',
                placement: 'topRight',
                duration: 4,
            });
            navigate('/auth');
            return;
        }
        
        try {
            await addToCart(productId, 1); // Add 1 quantity by default
            // Success notification is handled by CartContext
        } catch (error) {
            console.error('Error adding to cart:', error);
            // Error notification is handled by CartContext
        }
    };

    return (
        <div>
            <div className="grid gap-4 p-4" 
                 style={{ gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))' }}>
                {displayedProducts.map(product => (
                    <div
                        key={product.id}
                        onClick={() => handleProductClick(product.id)}
                        className="bg-white rounded-lg overflow-hidden shadow-sm border border-gray-100 hover:shadow-md hover:border-gray-200 transition-all duration-300 transform hover:-translate-y-1 cursor-pointer"
                    >
                        {/* Image container: square */}
                        <div className="relative w-full pb-[100%] bg-gray-100">
                            <img
                                src={getPrimaryProductImage(product) || product.images?.[0] || getFallbackImageUrl('product')}
                                alt={product.title}
                                className="absolute inset-0 w-full h-full object-cover"
                                onError={(e) => {
                                    e.target.src = getFallbackImageUrl('product');
                                }}
                            />
                        </div>

                        {/* Content */}
                        <div className="p-3 flex flex-col justify-between h-44">
                            <div>
                                {/* Title: clamp to 2 lines */}
                                <h3 className="text-sm font-medium text-gray-800 leading-tight mb-1" 
                                    style={{
                                        display: '-webkit-box',
                                        WebkitLineClamp: 2,
                                        WebkitBoxOrient: 'vertical',
                                        overflow: 'hidden',
                                        minHeight: '2.5em'
                                    }}>
                                    {product.title}
                                </h3>
                                
                                {/* Price */}
                                <p className="text-base font-bold text-[#ed2b2a] mb-1">
                                    ${product.price}
                                </p>
                                
                                {/* MOQ & Shipping */}
                                <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                                    <span>MOQ: 1 piece</span> 
                                    <span className="text-green-600">Free Shipping</span>
                                </div>
                            </div>

                            {/* Footer row: location + actions */}
                            <div className="flex items-center justify-between mt-auto pt-2 border-t border-gray-50">
                                <span className="text-xs text-gray-500 flex items-center">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-3 w-3 mr-1"
                                        fill="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path d="M12 2C8.14 2 5 5.14 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.86-3.14-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5S10.62 6.5 12 6.5s2.5 1.12 2.5 2.5S13.38 11.5 12 11.5z"/>
                                    </svg>
                                    CN
                                </span>
                                <div className="flex space-x-2">
                                    <button 
                                        onClick={(e) => handleCartClick(e, product.id)}
                                        disabled={cartLoading || !product.inStock}
                                        className={`transition-colors duration-200 text-sm p-1 rounded ${
                                            cartLoading || !product.inStock 
                                                ? 'text-gray-300 cursor-not-allowed' 
                                                : 'text-gray-400 hover:text-blue-600 cursor-pointer hover:bg-gray-50'
                                        }`}
                                        title={product.inStock ? "Add to Cart" : "Out of Stock"}
                                    >
                                        🛒
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
            
            {/* Pagination */}
            {totalProducts > pageSize && (
                <div className="flex justify-center p-4">
                    <Pagination
                        current={currentPage}
                        total={totalProducts}
                        pageSize={pageSize}
                        onChange={handlePageChange}
                        showSizeChanger={false}
                        showQuickJumper={true}
                        showTotal={(total, range) =>
                            `${range[0]}-${range[1]} of ${total} products`
                        }
                    />
                </div>
            )}
        </div>
    );
};

export default ProductCard;