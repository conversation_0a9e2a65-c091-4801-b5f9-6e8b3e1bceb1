import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'https://multi-vendor-server-1tb9.onrender.com';

// Create axios instance for public API calls
const publicApi = axios.create({
  baseURL: API_BASE_URL.endsWith('/api') ? `${API_BASE_URL}/public` : `${API_BASE_URL}/api/public`,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  withCredentials: true,
  timeout: 30000, // 30 seconds timeout
});

// Add request interceptor to handle CORS
publicApi.interceptors.request.use(
  (config) => {
    // Ensure proper headers for CORS
    config.headers['Accept'] = 'application/json';
    config.headers['Content-Type'] = 'application/json';
    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Handle response errors
publicApi.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Public API Error:', error);
    
    // Handle CORS errors specifically
    if (error.code === 'ERR_NETWORK' || error.message.includes('CORS')) {
      console.error('CORS Error detected. Please ensure the server is running on http://localhost:5000');
    }
    
    return Promise.reject(error);
  }
);

// Products APIs
export const productsApi = {
  // Get all products with pagination and filters
  getProducts: (params) => publicApi.get('/products', { params }),
  
  // Get single product by ID
  getProduct: (id) => publicApi.get(`/products/${id}`),
  
  // Get featured products
  getFeaturedProducts: (limit = 8) => publicApi.get(`/products/featured?limit=${limit}`),
  
  // Get new arrivals
  getNewArrivals: (limit = 8, days = 30) => publicApi.get(`/products/new-arrivals?limit=${limit}&days=${days}`),
  
  // Get best selling products
  getBestSelling: (limit = 8) => publicApi.get(`/products/best-selling?limit=${limit}`),

  // Get budget-friendly products
  getBudgetFriendlyProducts: (limit = 8) => publicApi.get(`/products/budget-friendly?limit=${limit}`),

  // Get products by category
  getProductsByCategory: (categoryId, params) => publicApi.get(`/products/category/${categoryId}`, { params }),
  
  // Get products by vendor
  getProductsByVendor: (vendorId, params) => publicApi.get(`/products/vendor/${vendorId}`, { params }),
  
  // Search products
  searchProducts: (query, params) => publicApi.get(`/search/products?q=${encodeURIComponent(query)}`, { params })
};

// Categories APIs
export const categoriesApi = {
  // Get all categories
  getCategories: (options = {}) => {
    console.log('Making categories API call to:', publicApi.defaults.baseURL + '/categories');
    return publicApi.get('/categories', {
      timeout: 15000, // 15 second timeout
      ...options
    });
  },
  
  // Get single category
  getCategory: (id) => publicApi.get(`/categories/${id}`),
  
  // Get category with products
  getCategoryWithProducts: (id, params) => publicApi.get(`/categories/${id}/products`, { params })
};

// Search APIs
export const searchApi = {
  // Global search
  search: (query, params) => publicApi.get(`/search?q=${encodeURIComponent(query)}`, { params }),
  
  // Search suggestions
  getSuggestions: (query) => publicApi.get(`/search/suggestions?q=${encodeURIComponent(query)}`)
};

export default publicApi;
