const mongoose = require('mongoose');

const homepageSettingsSchema = new mongoose.Schema({
  // Carousel Images Section
  carouselImages: [{
    title: {
      type: String,
      trim: true,
      maxlength: [100, 'Title cannot exceed 100 characters']
    },
    description: {
      type: String,
      trim: true,
      maxlength: [200, 'Description cannot exceed 200 characters']
    },
    imageUrl: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          // Validate that it's a valid URL (basic validation)
          return /^https?:\/\/.+/.test(v);
        },
        message: 'Image URL must be a valid HTTP/HTTPS URL'
      }
    },
    linkUrl: {
      type: String,
      trim: true
    },
    isActive: {
      type: Boolean,
      default: true
    },
    sortOrder: {
      type: Number,
      default: 0
    }
  }],

  // Featured Categories Section
  featuredCategories: [{
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Category',
      required: true
    },
    displayName: {
      type: String,
      trim: true,
      maxlength: [50, 'Display name cannot exceed 50 characters']
    },
    imageUrl: {
      type: String,
      required: true
    },
    cloudinaryPublicId: {
      type: String,
      required: true
    },
    isActive: {
      type: Boolean,
      default: true
    },
    sortOrder: {
      type: Number,
      default: 0
    }
  }],

  // Promotion Images Section (simplified to only title and image)
  promotionImages: [{
    title: {
      type: String,
      trim: true,
      maxlength: [100, 'Title cannot exceed 100 characters']
    },
    imageUrl: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          // Validate that it's a valid URL (basic validation)
          return /^https?:\/\/.+/.test(v);
        },
        message: 'Image URL must be a valid HTTP/HTTPS URL'
      }
    },
    position: {
      type: String,
      enum: ['sidebar', 'banner', 'popup', 'footer'],
      default: 'sidebar'
    },
    isActive: {
      type: Boolean,
      default: true
    },
    sortOrder: {
      type: Number,
      default: 0
    }
  }],

  // Featured Category IDs (simple array of category IDs)
  featuredCategoryIds: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category'
  }],

  // General Settings
  settings: {
    autoPlayCarousel: {
      type: Boolean,
      default: true
    },
    carouselSpeed: {
      type: Number,
      default: 3000,
      min: 1000,
      max: 10000
    },
    showPromotions: {
      type: Boolean,
      default: true
    },
    maxCarouselImages: {
      type: Number,
      default: 10,
      min: 1,
      max: 20
    },
    maxPromotionImages: {
      type: Number,
      default: 5,
      min: 1,
      max: 10
    }
  },

  // Metadata
  lastUpdatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
homepageSettingsSchema.index({ 'carouselImages.isActive': 1 });
homepageSettingsSchema.index({ 'carouselImages.sortOrder': 1 });
homepageSettingsSchema.index({ 'featuredCategories.isActive': 1 });
homepageSettingsSchema.index({ 'featuredCategories.sortOrder': 1 });
homepageSettingsSchema.index({ 'promotionImages.isActive': 1 });
homepageSettingsSchema.index({ 'promotionImages.position': 1 });
homepageSettingsSchema.index({ 'promotionImages.startDate': 1, 'promotionImages.endDate': 1 });

// Virtual for active carousel images
homepageSettingsSchema.virtual('activeCarouselImages').get(function() {
  return this.carouselImages
    .filter(img => img.isActive)
    .sort((a, b) => a.sortOrder - b.sortOrder);
});

// Virtual for active featured categories
homepageSettingsSchema.virtual('activeFeaturedCategories').get(function() {
  return this.featuredCategories
    .filter(cat => cat.isActive)
    .sort((a, b) => a.sortOrder - b.sortOrder);
});

// Virtual for active promotion images by position
homepageSettingsSchema.virtual('activePromotionsByPosition').get(function() {
  const now = new Date();
  return this.promotionImages
    .filter(promo => {
      if (!promo.isActive) return false;
      if (promo.startDate && promo.startDate > now) return false;
      if (promo.endDate && promo.endDate < now) return false;
      return true;
    })
    .reduce((acc, promo) => {
      if (!acc[promo.position]) acc[promo.position] = [];
      acc[promo.position].push(promo);
      return acc;
    }, {});
});

// Static method to get or create homepage settings
homepageSettingsSchema.statics.getSettings = async function() {
  let settings = await this.findOne();
  if (!settings) {
    settings = await this.create({
      carouselImages: [],
      featuredCategories: [],
      promotionImages: [],
      settings: {}
    });
  }
  return settings;
};

// Instance method to add carousel image
homepageSettingsSchema.methods.addCarouselImage = function(imageData) {
  this.carouselImages.push({
    ...imageData,
    sortOrder: this.carouselImages.length
  });
  return this.save();
};

// Instance method to add promotion image
homepageSettingsSchema.methods.addPromotionImage = function(imageData) {
  this.promotionImages.push({
    ...imageData,
    sortOrder: this.promotionImages.length
  });
  return this.save();
};

// Instance method to add featured category
homepageSettingsSchema.methods.addFeaturedCategory = function(categoryData) {
  this.featuredCategories.push({
    ...categoryData,
    sortOrder: this.featuredCategories.length
  });
  return this.save();
};

const HomepageSettings = mongoose.model('HomepageSettings', homepageSettingsSchema);

module.exports = HomepageSettings;
