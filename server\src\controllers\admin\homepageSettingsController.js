const HomepageSettings = require('../../models/HomepageSettings');
const Category = require('../../models/Category');
const { deleteImage, extractPublicId } = require('../../config/cloudinary');

// Get homepage settings
const getHomepageSettings = async (req, res) => {
  try {
    // Get settings with population
    let settings = await HomepageSettings.findOne()
      .populate('featuredCategories.category', 'name slug');

    // If no settings exist, create default settings
    if (!settings) {
      settings = await HomepageSettings.create({
        heroSection: {
          title: 'Welcome to Our Store',
          subtitle: 'Discover amazing products from trusted vendors',
          backgroundImage: '',
          ctaText: 'Shop Now',
          ctaLink: '/products'
        },
        featuredCategories: [],
        promotionalBanners: [],
        testimonials: []
      });
    }

    res.status(200).json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Error fetching homepage settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch homepage settings',
      error: error.message
    });
  }
};

// Update general settings
const updateGeneralSettings = async (req, res) => {
  try {
    const { autoPlayCarousel, carouselSpeed, showPromotions, maxCarouselImages, maxPromotionImages, featuredCategoryIds } = req.body;

    const settings = await HomepageSettings.getSettings();

    if (autoPlayCarousel !== undefined) settings.settings.autoPlayCarousel = autoPlayCarousel;
    if (carouselSpeed !== undefined) settings.settings.carouselSpeed = carouselSpeed;
    if (showPromotions !== undefined) settings.settings.showPromotions = showPromotions;
    if (maxCarouselImages !== undefined) settings.settings.maxCarouselImages = maxCarouselImages;
    if (maxPromotionImages !== undefined) settings.settings.maxPromotionImages = maxPromotionImages;
    if (featuredCategoryIds !== undefined) {
      // Limit to 5 categories
      settings.featuredCategoryIds = featuredCategoryIds.slice(0, 5);
    }

    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'Settings updated successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error updating settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update settings',
      error: error.message
    });
  }
};

// Add carousel image
const addCarouselImage = async (req, res) => {
  try {
    console.log('🎠 Adding carousel image...');
    console.log('📝 Request body:', req.body);

    const { title, description, linkUrl, imageUrl } = req.body;

    if (!imageUrl) {
      return res.status(400).json({
        success: false,
        message: 'Image URL is required'
      });
    }

    // Validate URL format
    if (!/^https?:\/\/.+/.test(imageUrl)) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a valid HTTP/HTTPS image URL'
      });
    }

    const settings = await HomepageSettings.getSettings();

    // Check if we've reached the maximum number of carousel images
    if (settings.carouselImages.length >= (settings.settings?.maxCarouselImages || 10)) {
      return res.status(400).json({
        success: false,
        message: `Maximum ${settings.settings?.maxCarouselImages || 10} carousel images allowed`
      });
    }

    const imageData = {
      title: title || '',
      description: description || '',
      imageUrl,
      linkUrl: linkUrl || '',
      isActive: true,
      sortOrder: settings.carouselImages.length
    };

    console.log('💾 Saving image data:', imageData);
    await settings.addCarouselImage(imageData);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    console.log('🎉 Carousel image saved successfully');
    res.status(201).json({
      success: true,
      message: 'Carousel image added successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error adding carousel image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add carousel image',
      error: error.message
    });
  }
};

// Update carousel image
const updateCarouselImage = async (req, res) => {
  try {
    const { imageId } = req.params;
    const { title, description, linkUrl, imageUrl, isActive, sortOrder } = req.body;

    const settings = await HomepageSettings.getSettings();
    const imageIndex = settings.carouselImages.findIndex(img => img._id.toString() === imageId);

    if (imageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Carousel image not found'
      });
    }

    const image = settings.carouselImages[imageIndex];

    // Update image URL if provided
    if (imageUrl !== undefined) {
      // Validate URL format
      if (imageUrl && !/^https?:\/\/.+/.test(imageUrl)) {
        return res.status(400).json({
          success: false,
          message: 'Please provide a valid HTTP/HTTPS image URL'
        });
      }
      image.imageUrl = imageUrl;
    }

    // Update other fields
    if (title !== undefined) image.title = title;
    if (description !== undefined) image.description = description;
    if (linkUrl !== undefined) image.linkUrl = linkUrl;
    if (isActive !== undefined) image.isActive = isActive;
    if (sortOrder !== undefined) image.sortOrder = sortOrder;

    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'Carousel image updated successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error updating carousel image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update carousel image',
      error: error.message
    });
  }
};

// Delete carousel image
const deleteCarouselImage = async (req, res) => {
  try {
    const { imageId } = req.params;

    const settings = await HomepageSettings.getSettings();
    const imageIndex = settings.carouselImages.findIndex(img => img._id.toString() === imageId);

    if (imageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Carousel image not found'
      });
    }

    // Remove image from array
    settings.carouselImages.splice(imageIndex, 1);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'Carousel image deleted successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error deleting carousel image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete carousel image',
      error: error.message
    });
  }
};

// Add promotion image
const addPromotionImage = async (req, res) => {
  try {
    const { title, imageUrl } = req.body;

    if (!imageUrl) {
      return res.status(400).json({
        success: false,
        message: 'Image URL is required'
      });
    }

    // Validate URL format
    if (!/^https?:\/\/.+/.test(imageUrl)) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a valid HTTP/HTTPS image URL'
      });
    }

    const settings = await HomepageSettings.getSettings();

    // Check if we've reached the maximum number of promotion images
    if (settings.promotionImages.length >= (settings.settings?.maxPromotionImages || 5)) {
      return res.status(400).json({
        success: false,
        message: `Maximum ${settings.settings?.maxPromotionImages || 5} promotion images allowed`
      });
    }

    const imageData = {
      title: title || '',
      imageUrl,
      position: 'sidebar', // Default position
      isActive: true,
      sortOrder: settings.promotionImages.length
    };

    await settings.addPromotionImage(imageData);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(201).json({
      success: true,
      message: 'Promotion image added successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error adding promotion image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add promotion image',
      error: error.message
    });
  }
};

// Update promotion image
const updatePromotionImage = async (req, res) => {
  try {
    const { imageId } = req.params;
    const { title, imageUrl, isActive, sortOrder } = req.body;

    const settings = await HomepageSettings.getSettings();
    const imageIndex = settings.promotionImages.findIndex(img => img._id.toString() === imageId);

    if (imageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Promotion image not found'
      });
    }

    const image = settings.promotionImages[imageIndex];

    // Update image URL if provided
    if (imageUrl !== undefined) {
      // Validate URL format
      if (imageUrl && !/^https?:\/\/.+/.test(imageUrl)) {
        return res.status(400).json({
          success: false,
          message: 'Please provide a valid HTTP/HTTPS image URL'
        });
      }
      image.imageUrl = imageUrl;
    }

    // Update other fields
    if (title !== undefined) image.title = title;
    if (isActive !== undefined) image.isActive = isActive;
    if (sortOrder !== undefined) image.sortOrder = sortOrder;

    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'Promotion image updated successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error updating promotion image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update promotion image',
      error: error.message
    });
  }
};

// Delete promotion image
const deletePromotionImage = async (req, res) => {
  try {
    const { imageId } = req.params;

    const settings = await HomepageSettings.getSettings();
    const imageIndex = settings.promotionImages.findIndex(img => img._id.toString() === imageId);

    if (imageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Promotion image not found'
      });
    }

    // Remove image from array
    settings.promotionImages.splice(imageIndex, 1);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'Promotion image deleted successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error deleting promotion image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete promotion image',
      error: error.message
    });
  }
};

// Add featured category
const addFeaturedCategory = async (req, res) => {
  try {
    const { categoryId, displayName } = req.body;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Image file is required'
      });
    }

    if (!categoryId) {
      return res.status(400).json({
        success: false,
        message: 'Category ID is required'
      });
    }

    const settings = await HomepageSettings.getSettings();

    // Check if we've reached the maximum number of featured categories (5)
    if (settings.featuredCategories.length >= 5) {
      return res.status(400).json({
        success: false,
        message: 'Maximum 5 featured categories allowed'
      });
    }

    // Check if category is already featured
    const existingCategory = settings.featuredCategories.find(
      cat => cat.category.toString() === categoryId
    );

    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: 'Category is already featured'
      });
    }

    const categoryData = {
      category: categoryId,
      displayName,
      imageUrl: req.file.path, // Cloudinary URL
      cloudinaryPublicId: req.file.filename, // Cloudinary public ID
      sortOrder: settings.featuredCategories.length
    };

    await settings.addFeaturedCategory(categoryData);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    // Populate the category data for response
    await settings.populate('featuredCategories.category', 'name slug');

    res.status(201).json({
      success: true,
      message: 'Featured category added successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error adding featured category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add featured category',
      error: error.message
    });
  }
};

// Update featured category
const updateFeaturedCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { displayName, isActive, sortOrder } = req.body;

    const settings = await HomepageSettings.getSettings();
    const categoryIndex = settings.featuredCategories.findIndex(
      cat => cat._id.toString() === categoryId
    );

    if (categoryIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Featured category not found'
      });
    }

    const category = settings.featuredCategories[categoryIndex];

    // Update image file if provided
    if (req.file) {
      // Delete old image from Cloudinary
      if (category.cloudinaryPublicId) {
        await deleteImage(category.cloudinaryPublicId);
      }

      category.imageUrl = req.file.path; // Cloudinary URL
      category.cloudinaryPublicId = req.file.filename; // Cloudinary public ID
    }

    // Update other fields
    if (displayName !== undefined) category.displayName = displayName;
    if (isActive !== undefined) category.isActive = isActive;
    if (sortOrder !== undefined) category.sortOrder = sortOrder;

    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    // Populate the category data for response
    await settings.populate('featuredCategories.category', 'name slug');

    res.status(200).json({
      success: true,
      message: 'Featured category updated successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error updating featured category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update featured category',
      error: error.message
    });
  }
};

// Delete featured category
const deleteFeaturedCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;

    const settings = await HomepageSettings.getSettings();
    const categoryIndex = settings.featuredCategories.findIndex(
      cat => cat._id.toString() === categoryId
    );

    if (categoryIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Featured category not found'
      });
    }

    const category = settings.featuredCategories[categoryIndex];

    // Delete image from Cloudinary
    if (category.cloudinaryPublicId) {
      await deleteImage(category.cloudinaryPublicId);
    }

    // Remove category from array
    settings.featuredCategories.splice(categoryIndex, 1);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();

    res.status(200).json({
      success: true,
      message: 'Featured category deleted successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error deleting featured category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete featured category',
      error: error.message
    });
  }
};

module.exports = {
  getHomepageSettings,
  updateGeneralSettings,
  addCarouselImage,
  updateCarouselImage,
  deleteCarouselImage,
  addPromotionImage,
  updatePromotionImage,
  deletePromotionImage,
  addFeaturedCategory,
  updateFeaturedCategory,
  deleteFeaturedCategory
};
