require('dotenv').config();
const mongoose = require('mongoose');

async function testConnection() {
    try {
        console.log('🔄 Testing connection to live MongoDB Atlas...');
        
        const MONGODB_URI = process.env.MONGODB_URI;
        console.log('📍 Connecting to:', MONGODB_URI.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@'));
        
        await mongoose.connect(MONGODB_URI, {
            serverSelectionTimeoutMS: 30000,
            socketTimeoutMS: 45000,
            maxPoolSize: 20,
            minPoolSize: 10,
            maxIdleTimeMS: 30000,
            retryWrites: true,
            w: 'majority'
        });
        
        console.log('✅ Connected successfully!');
        
        // Test a simple query
        const collections = await mongoose.connection.db.listCollections().toArray();
        console.log(`📊 Found ${collections.length} collections:`);
        
        for (const collection of collections) {
            const count = await mongoose.connection.db.collection(collection.name).countDocuments();
            console.log(`   - ${collection.name}: ${count} documents`);
        }
        
        console.log('🎉 Live database is working perfectly!');
        
    } catch (error) {
        console.error('❌ Connection failed:', error.message);
    } finally {
        await mongoose.connection.close();
        console.log('🔌 Connection closed');
        process.exit(0);
    }
}

testConnection();