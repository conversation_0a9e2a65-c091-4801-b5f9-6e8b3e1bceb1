import React, { useState, useEffect } from 'react';
import { Select, Input, Form } from 'antd';
import { PhoneOutlined, GlobalOutlined } from '@ant-design/icons';
import { countries, getCountryByCode, validatePhoneNumber, formatPhoneNumber, getDefaultCountry } from '../../utils/countries';
import '../../styles/PhoneInput.css';

const { Option } = Select;

const PhoneInput = ({ 
  value, 
  onChange, 
  placeholder = "Enter phone number",
  disabled = false,
  required = false,
  showValidation = false, // Changed default to false to avoid duplicate messages
  className = "",
  size = "large",
  status // Add status prop from Form.Item
}) => {
  const [selectedCountry, setSelectedCountry] = useState(getDefaultCountry());
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isValid, setIsValid] = useState(true);

  // Parse initial value if provided
  useEffect(() => {
    if (value && typeof value === 'object' && value.phone && value.countryCode) {
      const country = getCountryByCode(value.countryCode);
      if (country) {
        setSelectedCountry(country);
        setPhoneNumber(value.phone);
      }
    } else if (value && typeof value === 'string') {
      // Try to parse phone number string
      const country = countries.find(c => value.startsWith(c.dialCode));
      if (country) {
        setSelectedCountry(country);
        setPhoneNumber(value);
      } else {
        setPhoneNumber(value);
      }
    }
  }, [value]);

  // Validate phone number when it changes
  useEffect(() => {
    if (phoneNumber && selectedCountry) {
      const valid = validatePhoneNumber(phoneNumber, selectedCountry.code);
      setIsValid(valid);
    } else {
      setIsValid(!required || !phoneNumber);
    }
  }, [phoneNumber, selectedCountry, required]);

  const handleCountryChange = (countryCode) => {
    const country = getCountryByCode(countryCode);
    if (country) {
      setSelectedCountry(country);
      
      // Update phone number with new country code if it doesn't already have it
      let newPhoneNumber = phoneNumber;
      if (phoneNumber && !phoneNumber.startsWith(country.dialCode)) {
        // Remove old country code if present
        const oldCountryCode = countries.find(c => phoneNumber.startsWith(c.dialCode));
        if (oldCountryCode) {
          newPhoneNumber = phoneNumber.replace(oldCountryCode.dialCode, '');
        }
        // Add new country code
        newPhoneNumber = country.dialCode + newPhoneNumber.replace(/^\+?/, '');
      } else if (!phoneNumber) {
        newPhoneNumber = country.dialCode;
      }
      
      setPhoneNumber(newPhoneNumber);
      
      if (onChange) {
        onChange({
          phone: newPhoneNumber,
          countryCode: country.code,
          formatted: formatPhoneNumber(newPhoneNumber, country.code),
          isValid: validatePhoneNumber(newPhoneNumber, country.code)
        });
      }
    }
  };

  const handlePhoneChange = (e) => {
    let newPhoneNumber = e.target.value;
    
    // Ensure the phone number starts with the selected country's dial code
    if (selectedCountry && newPhoneNumber && !newPhoneNumber.startsWith(selectedCountry.dialCode)) {
      // If user is typing and doesn't have country code, add it
      if (!newPhoneNumber.startsWith('+')) {
        newPhoneNumber = selectedCountry.dialCode + newPhoneNumber;
      }
    }
    
    // Only allow digits, +, spaces, hyphens, and parentheses
    newPhoneNumber = newPhoneNumber.replace(/[^\d+\s\-()]/g, '');
    
    setPhoneNumber(newPhoneNumber);
    
    if (onChange) {
      onChange({
        phone: newPhoneNumber,
        countryCode: selectedCountry.code,
        formatted: formatPhoneNumber(newPhoneNumber, selectedCountry.code),
        isValid: validatePhoneNumber(newPhoneNumber, selectedCountry.code)
      });
    }
  };

  const countrySelectBefore = (
    <Select
      value={selectedCountry?.code}
      onChange={handleCountryChange}
      disabled={disabled}
      style={{ width: 100 }}
      size={size}
      showSearch
      optionFilterProp="children"
      filterOption={(input, option) =>
        option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
        option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
      }
      dropdownStyle={{ maxHeight: 300 }}
    >
      {countries.map(country => (
        <Option key={country.code} value={country.code}>
          <span style={{ marginRight: 8 }}>{country.flag}</span>
          {country.dialCode}
        </Option>
      ))}
    </Select>
  );

  return (
    <div className={`phone-input-container ${className}`}>
      <Input
        addonBefore={countrySelectBefore}
        value={phoneNumber}
        onChange={handlePhoneChange}
        placeholder={placeholder}
        disabled={disabled}
        size={size}
        prefix={<PhoneOutlined className="text-gray-400" />}
        status={showValidation && phoneNumber && !isValid ? 'error' : ''}
        className={`phone-input ${!isValid && showValidation ? 'border-red-500' : ''}`}
      />
      
      {showValidation && phoneNumber && !isValid && (
        <div className="text-red-500 text-sm mt-1">
          Please enter a valid phone number for {selectedCountry?.name}
        </div>
      )}
      
      {phoneNumber && isValid && selectedCountry && (
        <div className="text-gray-500 text-sm mt-1">
          Format: {formatPhoneNumber(phoneNumber, selectedCountry.code)}
        </div>
      )}
    </div>
  );
};

// Form.Item wrapper for easier integration with Ant Design forms
export const PhoneFormItem = ({ 
  name, 
  label = "Phone Number", 
  rules = [], 
  required = false,
  disabled = false,
  ...props 
}) => {
  const defaultRules = [
    ...(required ? [{ required: true, message: 'Please enter your phone number' }] : []),
    {
      validator: (_, value) => {
        if (!value || !value.phone) {
          return required ? Promise.reject('Please enter your phone number') : Promise.resolve();
        }
        if (!value.isValid) {
          // Get country name for better error message
          const country = getCountryByCode(value.countryCode);
          const countryName = country ? country.name : 'the selected country';
          return Promise.reject(`Please enter a valid phone number for ${countryName}`);
        }
        return Promise.resolve();
      }
    },
    ...rules
  ];

  return (
    <Form.Item
      name={name}
      label={label}
      rules={defaultRules}
      {...props}
    >
      <PhoneInput 
        required={required}
        disabled={disabled}
        showValidation={false} // Disable component-level validation to avoid duplicates
        {...props}
      />
    </Form.Item>
  );
};

export default PhoneInput;