# Development Environment Configuration
# App Configuration
APP_NAME=Alicartify
VITE_APP_NAME=Alicartify
VITE_APP_VERSION=1.0.0

# API Configuration - Development
VITE_API_URL=http://localhost:5000/api
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3
VITE_API_RETRY_DELAY=1000

# Analytics Configuration - Development
VITE_ANALYTICS_REFRESH_INTERVAL=10000
VITE_ANALYTICS_CACHE_TIMEOUT=30000
VITE_ANALYTICS_REAL_TIME=true
VITE_ANALYTICS_DEBUG=true

# Dashboard Configuration - Development
VITE_DASHBOARD_AUTO_REFRESH=true
VITE_DASHBOARD_REFRESH_INTERVAL=30000
VITE_DASHBOARD_MAX_RETRIES=5
VITE_DASHBOARD_FALLBACK_SAMPLE=false

# Performance Configuration - Development
VITE_ENABLE_CACHING=true
VITE_ENABLE_COMPRESSION=false
VITE_ENABLE_LAZY_LOADING=true

# Debug Configuration - Development
VITE_DEBUG_MODE=true
