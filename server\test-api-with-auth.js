const axios = require('axios');
const dotenv = require('dotenv');

dotenv.config();

const API_BASE_URL = 'http://localhost:5000/api';

async function testAdminAPI() {
  try {
    console.log('🔐 Logging in with admin credentials...');
    
    // Step 1: Login to get token
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password@admin123'
    });

    console.log('✅ Login successful!');
    console.log('User type:', loginResponse.data.data.user.userType);
    
    const token = loginResponse.data.data.token;
    console.log('🎟️ Token received:', token.substring(0, 20) + '...');

    // Step 2: Test the product stats endpoint
    console.log('\n📊 Testing product stats endpoint...');
    
    const statsResponse = await axios.get(`${API_BASE_URL}/admin/products/stats`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Product stats API successful!');
    console.log('\n=== API RESPONSE ===');
    console.log(JSON.stringify(statsResponse.data, null, 2));

    // Step 3: Extract the key values that should appear on dashboard
    const stats = statsResponse.data.data.stats;
    console.log('\n=== DASHBOARD VALUES ===');
    console.log('Total Products:', stats.totalProducts);
    console.log('Active Products:', stats.activeProducts);
    console.log('Draft Products:', stats.draftProducts);
    console.log('Featured Products:', stats.featuredProducts);
    console.log('Low Stock Products:', stats.lowStockProducts);

    // Step 4: Test dashboard stats endpoint too
    console.log('\n🏠 Testing dashboard stats endpoint...');
    
    const dashboardResponse = await axios.get(`${API_BASE_URL}/admin/dashboard/stats`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Dashboard stats API successful!');
    const dashboardProducts = dashboardResponse.data.data.overview.products;
    console.log('\n=== DASHBOARD OVERVIEW PRODUCTS ===');
    console.log('Total Products (from dashboard):', dashboardProducts?.totalProducts);
    console.log('Active Products (from dashboard):', dashboardProducts?.activeProducts);

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    console.error('Status:', error.response?.status);
    console.error('URL:', error.config?.url);
    if (error.response?.status === 401) {
      console.error('🔒 Authentication failed - check credentials');
    } else if (error.response?.status === 404) {
      console.error('🔍 Endpoint not found - check URL');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('🔌 Connection refused - is the server running on localhost:5000?');
    }
  }
}

testAdminAPI();
