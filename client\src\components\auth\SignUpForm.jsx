import React, { useState } from 'react';
import { Form, Checkbox, Select } from 'antd';
import { 
  UserOutlined, 
  MailOutlined, 
  LockOutlined, 
  ShopOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import { PhoneFormItem } from '../ui/PhoneInput';

const { Option } = Select;

const SignUpForm = ({ userType, onRegister }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      if (onRegister) {
        // Remove confirmPassword from the data before sending to server
        const { confirmPassword, phoneData, ...registerData } = values;
        
        // Extract phone information
        const finalData = { 
          ...registerData, 
          userType,
          phone: phoneData?.phone || '',
          countryCode: phoneData?.countryCode || 'US'
        };
        
        const result = await onRegister(finalData);
        if (!result.success) {
          console.error('Registration failed:', result.error);
        }
      } else {
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('SignUp values:', { ...values, userType });
      }
    } catch (error) {
      console.error('SignUp error:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderUserFields = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
      <Form.Item
        name="firstName"
        rules={[{ required: true, message: 'Please enter your first name' }]}
      >
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <UserOutlined className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="First Name"
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
          />
        </div>
      </Form.Item>

      <Form.Item
        name="lastName"
        rules={[{ required: true, message: 'Please enter your last name' }]}
      >
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <UserOutlined className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Last Name"
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
          />
        </div>
      </Form.Item>
    </div>
  );

  const renderVendorFields = () => (
    <>
      {/* Personal Information */}
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-800 mb-3">Personal Information</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <Form.Item
            name="firstName"
            rules={[{ required: true, message: 'Please enter your first name' }]}
          >
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <UserOutlined className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="First Name"
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
              />
            </div>
          </Form.Item>

          <Form.Item
            name="lastName"
            rules={[{ required: true, message: 'Please enter your last name' }]}
          >
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <UserOutlined className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Last Name"
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
              />
            </div>
          </Form.Item>
        </div>
      </div>

      {/* Business Information */}
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-800 mb-3">Business Information</h3>
        <Form.Item
          name="businessName"
          rules={[{ required: true, message: 'Please enter your store/business name' }]}
        >
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <ShopOutlined className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Store/Business Name"
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
            />
          </div>
        </Form.Item>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <Form.Item
            name="businessType"
            rules={[{ required: true, message: 'Please select your business type' }]}
          >
            <Select
              size="large"
              placeholder="Business Type"
              className="custom-select"
              style={{ height: '48px' }}
            >
              <Option value="retail">Retail</Option>
              <Option value="wholesale">Wholesale</Option>
              <Option value="manufacturer">Manufacturer</Option>
              <Option value="dropshipping">Dropshipping</Option>
              <Option value="services">Services</Option>
              <Option value="other">Other</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="contactPerson"
            rules={[{ required: false, message: 'Please enter contact person name' }]}
          >
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <UserOutlined className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Contact Person (Optional)"
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
              />
            </div>
          </Form.Item>
        </div>
      </div>
    </>
  );

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Create your account
        </h2>
        <p className="text-gray-600">
          Join as a {userType} and start your journey
        </p>
      </div>

      <Form
        form={form}
        onFinish={handleSubmit}
        layout="vertical"
        requiredMark={false}
        className="space-y-6"
      >
        {userType === 'customer' ? renderUserFields() : renderVendorFields()}

        <Form.Item
          name="email"
          rules={[
            { required: true, message: 'Please enter your email' },
            { type: 'email', message: 'Please enter a valid email' }
          ]}
        >
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MailOutlined className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="email"
              placeholder="Email Address"
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
            />
          </div>
        </Form.Item>

        <PhoneFormItem
          name="phoneData"
          label="Phone Number"
          required={true}
          placeholder="Enter your phone number"
          className="mb-6"
        />

        <Form.Item
          name="password"
          rules={[
            { required: true, message: 'Please enter your password' },
            { min: 8, message: 'Password must be at least 8 characters' },
            {
              pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
              message: 'Password must contain uppercase, lowercase and number'
            }
          ]}
        >
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <LockOutlined className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type={showPassword ? "text" : "password"}
              placeholder="Create Password"
              className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              {showPassword ? (
                <EyeInvisibleOutlined className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              ) : (
                <EyeOutlined className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              )}
            </button>
          </div>
        </Form.Item>

        <Form.Item
          name="confirmPassword"
          dependencies={['password']}
          rules={[
            { required: true, message: 'Please confirm your password' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('Passwords do not match'));
              },
            }),
          ]}
        >
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <LockOutlined className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type={showConfirmPassword ? "text" : "password"}
              placeholder="Confirm Password"
              className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              {showConfirmPassword ? (
                <EyeInvisibleOutlined className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              ) : (
                <EyeOutlined className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              )}
            </button>
          </div>
        </Form.Item>

        <div className="mb-6">
          <Checkbox
            checked={agreeTerms}
            onChange={(e) => setAgreeTerms(e.target.checked)}
            className="text-gray-600"
          >
            I agree to the{' '}
            <a href="#" className="text-orange-600 hover:text-orange-700 font-medium">
              Terms of Service
            </a>{' '}
            and{' '}
            <a href="#" className="text-orange-600 hover:text-orange-700 font-medium">
              Privacy Policy
            </a>
          </Checkbox>
        </div>

        <Form.Item>
          <button
            type="submit"
            disabled={loading || !agreeTerms}
            className="w-full bg-gradient-to-r from-orange-500 to-orange-600 text-white py-3 px-4 rounded-lg font-medium hover:from-orange-600 hover:to-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Creating account...
              </div>
            ) : (
              'Create Account'
            )}
          </button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default SignUpForm;