import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { sharedApi } from '../services/sharedApi';

const CurrencyContext = createContext();

export const useCurrency = () => {
  const context = useContext(CurrencyContext);
  if (!context) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
};

export const CurrencyProvider = ({ children }) => {
  const [currentCurrency, setCurrentCurrency] = useState('INR'); // Default currency
  const [supportedCurrencies, setSupportedCurrencies] = useState([]);
  const [loading, setLoading] = useState(false);

  // Fetch supported currencies from API
  const fetchSupportedCurrencies = useCallback(async () => {
    try {
      setLoading(true);
      const response = await sharedApi.getSupportedCurrencies();
      
      if (response.data.success) {
        const currencies = response.data.data.currencies || [];
        setSupportedCurrencies(currencies);
        
        // Set default currency if not already set
        if (!currentCurrency && response.data.data.defaultCurrency) {
          setCurrentCurrency(response.data.data.defaultCurrency);
        }
      }
    } catch (error) {
      console.error('Error fetching supported currencies:', error);
      // Fallback to basic currencies if API fails
      setSupportedCurrencies([
        { code: 'USD', name: 'US Dollar', symbol: '$' },
        { code: 'EUR', name: 'Euro', symbol: '€' },
        { code: 'GBP', name: 'British Pound', symbol: '£' },
        { code: 'INR', name: 'Indian Rupee', symbol: '₹' },
        { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
        { code: 'CNY', name: 'Chinese Yuan', symbol: '¥' },
        { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
        { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
        { code: 'SGD', name: 'Singapore Dollar', symbol: 'S$' },
        { code: 'AED', name: 'UAE Dirham', symbol: 'د.إ' }
      ]);
    } finally {
      setLoading(false);
    }
  }, [currentCurrency]);

  // Change currency
  const changeCurrency = useCallback(async (newCurrency) => {
    try {
      setLoading(true);
      
      // Update current currency in state
      setCurrentCurrency(newCurrency);
      
      // Try to update user preference if user is logged in
      try {
        const authToken = localStorage.getItem('authToken') || localStorage.getItem('token');
        if (authToken) {
          await sharedApi.updateUserCurrency(newCurrency);
        }
      } catch (error) {
        // If user is not logged in or API fails, just store locally
        console.log('User not logged in or API failed, storing currency locally');
      }
      
      // Store in localStorage for persistence
      localStorage.setItem('selectedCurrency', newCurrency);
      
      message.success(`Currency changed to ${newCurrency}`);
    } catch (error) {
      console.error('Error changing currency:', error);
      message.error('Failed to change currency');
    } finally {
      setLoading(false);
    }
  }, []);

  // Get currency symbol
  const getCurrencySymbol = useCallback((currency = currentCurrency) => {
    const currencyInfo = supportedCurrencies.find(c => c.code === currency);
    return currencyInfo?.symbol || currency;
  }, [currentCurrency, supportedCurrencies]);

  // Get currency name
  const getCurrencyName = useCallback((currency = currentCurrency) => {
    const currencyInfo = supportedCurrencies.find(c => c.code === currency);
    return currencyInfo?.name || currency;
  }, [currentCurrency, supportedCurrencies]);

  // Filter products by current currency availability
  const filterProductsByCurrency = useCallback((products, currency = currentCurrency) => {
    console.log('🏷️ Currency filtering - Input:', products.length, 'products for currency:', currency);
    if (!products || !Array.isArray(products)) {
      console.log('❌ No products array provided');
      return [];
    }
    
    const filtered = products.filter(product => {
      console.log(`\n🔍 Testing product: ${product.name || product._id}`);
      
      // Check if product has multi-currency pricing (array format)
      if (product.multiCurrencyPricing && Array.isArray(product.multiCurrencyPricing)) {
        console.log('✅ Has multiCurrencyPricing array:', product.multiCurrencyPricing);
        const hasArrayCurrency = product.multiCurrencyPricing.some(pricing => pricing.currency === currency);
        console.log(`Array format has ${currency}:`, hasArrayCurrency);
        if (hasArrayCurrency) return true;
      }
      
      // Check if product has multi-currency pricing (object format)
      if (product.pricing && product.pricing.multiCurrency && typeof product.pricing.multiCurrency === 'object') {
        console.log('✅ Has multiCurrency object:', product.pricing.multiCurrency);
        const hasObjectCurrency = product.pricing.multiCurrency.hasOwnProperty(currency);
        console.log(`Object format has ${currency}:`, hasObjectCurrency);
        if (hasObjectCurrency) return true;
      }
      
      // Check if product has basic pricing and currency matches
      if (product.pricing && (product.pricing.basePrice || product.pricing.salePrice)) {
        console.log('✅ Has basic pricing:', product.pricing);
        const productCurrency = product.pricing.currency || 'INR';
        console.log(`Product currency: ${productCurrency}, Target currency: ${currency}`);
        if (productCurrency === currency) return true;
        
        // More lenient fallback: if target currency is INR and product has basic pricing, include it
        if (currency === 'INR' && !product.pricing.currency) {
          console.log('✅ Including product with basic pricing for INR fallback');
          return true;
        }
        
        // Additional fallback: if product has any pricing, include it for now
        console.log('✅ Including product with any pricing as fallback');
        return true;
      }
      
      console.log('❌ Product filtered out - no pricing information');
      return false;
    });
    
    console.log(`💰 Currency filtering result: ${filtered.length} products kept from ${products.length}`);
    return filtered;
  }, [currentCurrency]);

  // Get product price in current currency
  const getProductPriceInCurrency = useCallback((product, currency = currentCurrency) => {
    // Check if product has multi-currency pricing (array format)
    if (product.multiCurrencyPricing && Array.isArray(product.multiCurrencyPricing)) {
      const currencyPricing = product.multiCurrencyPricing.find(pricing => pricing.currency === currency);
      if (currencyPricing) {
        return {
          basePrice: currencyPricing.basePrice,
          salePrice: currencyPricing.salePrice,
          currency: currencyPricing.currency
        };
      }
    }
    
    // Check if product has multi-currency pricing (object format)
    if (product.pricing && product.pricing.multiCurrency && typeof product.pricing.multiCurrency === 'object' && product.pricing.multiCurrency[currency]) {
      const currencyPricing = product.pricing.multiCurrency[currency];
      return {
        basePrice: currencyPricing.basePrice,
        salePrice: currencyPricing.salePrice,
        currency: currency
      };
    }
    
    // Check if product has basic pricing and currency matches
    if (product.pricing && (product.pricing.basePrice || product.pricing.salePrice)) {
      const productCurrency = product.pricing.currency || 'INR';
      if (productCurrency === currency) {
        return {
          basePrice: product.pricing.basePrice,
          salePrice: product.pricing.salePrice,
          currency: productCurrency
        };
      }
      
      // Fallback: if target currency is INR and product has basic pricing, use it
      if (currency === 'INR' && !product.pricing.currency) {
        return {
          basePrice: product.pricing.basePrice,
          salePrice: product.pricing.salePrice,
          currency: 'INR'
        };
      }
    }
    
    return null;
  }, [currentCurrency]);

  // Initialize currency on mount
  useEffect(() => {
    const initializeCurrency = async () => {
      // First check localStorage
      const storedCurrency = localStorage.getItem('selectedCurrency');
      if (storedCurrency) {
        setCurrentCurrency(storedCurrency);
      }

      // Fetch supported currencies
      await fetchSupportedCurrencies();

      // Try to get user's preferred currency if logged in
      try {
        const authToken = localStorage.getItem('authToken') || localStorage.getItem('token');
        if (authToken) {
          const response = await sharedApi.getUserCurrency();
          if (response.data.success && response.data.data.currency) {
            setCurrentCurrency(response.data.data.currency);
            localStorage.setItem('selectedCurrency', response.data.data.currency);
          }
        }
      } catch (error) {
        console.log('User not logged in or failed to get user currency preference');
      }
    };

    initializeCurrency();
  }, [fetchSupportedCurrencies]);

  const value = {
    // State
    currentCurrency,
    supportedCurrencies,
    loading,
    
    // Actions
    changeCurrency,
    getCurrencySymbol,
    getCurrencyName,
    fetchSupportedCurrencies,
    filterProductsByCurrency,
    getProductPriceInCurrency
  };

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  );
};
