const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const { User, Vendor } = require('../models');

let io;

const initializeSocket = (server) => {
  io = new Server(server, {
    cors: {
      origin: process.env.CLIENT_URL || "http://localhost:3000",
      methods: ["GET", "POST"],
      credentials: true
    }
  });

  // Socket authentication middleware
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      const userType = socket.handshake.auth.userType;
      const userId = socket.handshake.auth.userId;

      if (!token) {
        return next(new Error('Authentication error: No token provided'));
      }

      // Verify JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // Get user data
      const user = await User.findById(decoded.userId || decoded.id);
      if (!user) {
        return next(new Error('Authentication error: User not found'));
      }

      // If vendor, get vendor data
      let vendor = null;
      if (userType === 'vendor') {
        vendor = await Vendor.findOne({ user: user._id });
        if (!vendor) {
          return next(new Error('Authentication error: Vendor profile not found'));
        }
      }

      // Attach user data to socket
      socket.userId = user._id.toString();
      socket.userType = userType;
      socket.vendorId = vendor ? vendor._id.toString() : null;
      socket.user = user;
      socket.vendor = vendor;

      console.log(`Socket authenticated: ${user.email} (${userType})`);
      next();
    } catch (error) {
      console.error('Socket authentication error:', error);
      next(new Error('Authentication error: Invalid token'));
    }
  });

  io.on('connection', (socket) => {
    console.log(`User connected: ${socket.user.email} (${socket.userType})`);

    // Join appropriate rooms based on user type
    if (socket.userType === 'admin') {
      socket.join('admins');
      console.log(`Admin ${socket.user.email} joined admin room`);
    } else if (socket.userType === 'vendor' && socket.vendorId) {
      socket.join(`vendor_${socket.vendorId}`);
      console.log(`Vendor ${socket.user.email} joined vendor room: vendor_${socket.vendorId}`);
    }

    // Handle order status updates
    socket.on('orderStatusUpdate', async (data) => {
      try {
        const { orderId, status, updatedBy, userType: updaterType } = data;
        
        console.log(`Order ${orderId} status updated to ${status} by ${updaterType} ${updatedBy}`);

        // Broadcast to admins
        socket.to('admins').emit('orderStatusUpdated', {
          orderId,
          status,
          updatedBy,
          updaterType,
          timestamp: new Date(),
          orderNumber: data.orderNumber
        });

        // If updated by admin, notify relevant vendor
        if (updaterType === 'admin' && data.vendorId) {
          socket.to(`vendor_${data.vendorId}`).emit('orderStatusUpdated', {
            orderId,
            status,
            updatedBy,
            updaterType,
            timestamp: new Date(),
            orderNumber: data.orderNumber
          });
        }

        // If updated by vendor, notify admins
        if (updaterType === 'vendor') {
          socket.to('admins').emit('orderStatusUpdated', {
            orderId,
            status,
            updatedBy,
            updaterType,
            timestamp: new Date(),
            orderNumber: data.orderNumber
          });
        }

      } catch (error) {
        console.error('Error handling order status update:', error);
        socket.emit('error', { message: 'Failed to broadcast order update' });
      }
    });

    // Handle new order notifications
    socket.on('newOrder', async (data) => {
      try {
        const { orderId, orderNumber, vendorId, customerId, totalAmount } = data;
        
        console.log(`New order ${orderNumber} created`);

        // Notify admins
        socket.to('admins').emit('newOrder', {
          orderId,
          orderNumber,
          vendorId,
          customerId,
          totalAmount,
          timestamp: new Date()
        });

        // Notify relevant vendor
        if (vendorId) {
          socket.to(`vendor_${vendorId}`).emit('newOrder', {
            orderId,
            orderNumber,
            vendorId,
            customerId,
            totalAmount,
            timestamp: new Date()
          });
        }

      } catch (error) {
        console.error('Error handling new order notification:', error);
        socket.emit('error', { message: 'Failed to broadcast new order' });
      }
    });

    // Handle inventory updates
    socket.on('inventoryUpdate', async (data) => {
      try {
        const { productId, vendorId, stock, lowStockThreshold } = data;
        
        // Notify admins about inventory changes
        socket.to('admins').emit('inventoryUpdated', {
          productId,
          vendorId,
          stock,
          lowStockThreshold,
          isLowStock: stock <= lowStockThreshold,
          timestamp: new Date()
        });

        // Notify the specific vendor
        if (vendorId) {
          socket.to(`vendor_${vendorId}`).emit('inventoryUpdated', {
            productId,
            stock,
            lowStockThreshold,
            isLowStock: stock <= lowStockThreshold,
            timestamp: new Date()
          });
        }

      } catch (error) {
        console.error('Error handling inventory update:', error);
        socket.emit('error', { message: 'Failed to broadcast inventory update' });
      }
    });

    // Handle vendor status changes
    socket.on('vendorStatusUpdate', async (data) => {
      try {
        const { vendorId, status, updatedBy } = data;
        
        // Notify the specific vendor
        socket.to(`vendor_${vendorId}`).emit('vendorStatusUpdated', {
          vendorId,
          status,
          updatedBy,
          timestamp: new Date()
        });

        // Notify admins
        socket.to('admins').emit('vendorStatusUpdated', {
          vendorId,
          status,
          updatedBy,
          timestamp: new Date()
        });

      } catch (error) {
        console.error('Error handling vendor status update:', error);
        socket.emit('error', { message: 'Failed to broadcast vendor status update' });
      }
    });

    // Handle disconnect
    socket.on('disconnect', () => {
      console.log(`User disconnected: ${socket.user.email} (${socket.userType})`);
    });

    // Handle connection errors
    socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  });

  return io;
};

// Helper functions to emit events from other parts of the application
const emitOrderUpdate = (orderId, orderNumber, status, vendorId, updatedBy = 'system') => {
  if (io) {
    // Notify admins
    io.to('admins').emit('orderStatusUpdated', {
      orderId,
      orderNumber,
      status,
      updatedBy,
      updaterType: 'system',
      timestamp: new Date()
    });

    // Notify relevant vendor
    if (vendorId) {
      io.to(`vendor_${vendorId}`).emit('orderStatusUpdated', {
        orderId,
        orderNumber,
        status,
        updatedBy,
        updaterType: 'system',
        timestamp: new Date()
      });
    }
  }
};

const emitNewOrder = (orderId, orderNumber, vendorId, customerId, totalAmount) => {
  if (io) {
    // Notify admins
    io.to('admins').emit('newOrder', {
      orderId,
      orderNumber,
      vendorId,
      customerId,
      totalAmount,
      timestamp: new Date()
    });

    // Notify relevant vendor
    if (vendorId) {
      io.to(`vendor_${vendorId}`).emit('newOrder', {
        orderId,
        orderNumber,
        vendorId,
        customerId,
        totalAmount,
        timestamp: new Date()
      });
    }
  }
};

const emitInventoryUpdate = (productId, vendorId, stock, lowStockThreshold) => {
  if (io) {
    const isLowStock = stock <= lowStockThreshold;
    
    // Notify admins
    io.to('admins').emit('inventoryUpdated', {
      productId,
      vendorId,
      stock,
      lowStockThreshold,
      isLowStock,
      timestamp: new Date()
    });

    // Notify the specific vendor
    if (vendorId) {
      io.to(`vendor_${vendorId}`).emit('inventoryUpdated', {
        productId,
        stock,
        lowStockThreshold,
        isLowStock,
        timestamp: new Date()
      });
    }
  }
};

const emitVendorStatusUpdate = (vendorId, status, updatedBy = 'system') => {
  if (io) {
    // Notify the specific vendor
    io.to(`vendor_${vendorId}`).emit('vendorStatusUpdated', {
      vendorId,
      status,
      updatedBy,
      timestamp: new Date()
    });

    // Notify admins
    io.to('admins').emit('vendorStatusUpdated', {
      vendorId,
      status,
      updatedBy,
      timestamp: new Date()
    });
  }
};

// Get online users count
const getOnlineUsers = () => {
  if (io) {
    return {
      total: io.engine.clientsCount,
      admins: io.sockets.adapter.rooms.get('admins')?.size || 0,
      vendors: Array.from(io.sockets.adapter.rooms.keys())
        .filter(room => room.startsWith('vendor_')).length
    };
  }
  return { total: 0, admins: 0, vendors: 0 };
};

module.exports = {
  initializeSocket,
  emitOrderUpdate,
  emitNewOrder,
  emitInventoryUpdate,
  emitVendorStatusUpdate,
  getOnlineUsers
};
