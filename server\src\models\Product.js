const mongoose = require('mongoose');

// Import modular components
const { addProductIndexes } = require('./product/productIndexes');
const { addProductVirtuals } = require('./product/productVirtuals');
const { addProductMiddleware } = require('./product/productMiddleware');
const { addProductStatics } = require('./product/productStatics');
const { addProductMethods } = require('./product/productMethods');
const { addProductCurrencyMethods } = require('./product/productCurrencyMethods');

// Import schema components
const { pricingSchema } = require('./schemas/pricingSchema');
const { inventorySchema } = require('./schemas/inventorySchema');
const { imageSchema } = require('./schemas/imageSchema');
const { variantSchema } = require('./schemas/variantSchema');
const { attributeSchema } = require('./schemas/attributeSchema');
const { colorSchema } = require('./schemas/colorSchema');
const { sizeSchema } = require('./schemas/sizeSchema');
const { deliveryChargesSchema } = require('./schemas/deliveryChargesSchema');
const { shippingOptionsSchema } = require('./schemas/shippingOptionsSchema');
const { returnPolicySchema } = require('./schemas/returnPolicySchema');
const { warrantySchema } = require('./schemas/warrantySchema');
const { specificationsSchema } = require('./schemas/specificationsSchema');
const { shippingSchema } = require('./schemas/shippingSchema');
const { seoSchema } = require('./schemas/seoSchema');
const { approvalSchema } = require('./schemas/approvalSchema');
const { reviewsSchema } = require('./schemas/reviewsSchema');
const { salesSchema } = require('./schemas/salesSchema');
const { analyticsSchema } = require('./schemas/analyticsSchema');

const productSchema = new mongoose.Schema({
  vendor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vendor',
    required: true
  },
  name: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [200, 'Product name cannot exceed 200 characters']
  },
  slug: {
    type: String,
    unique: true,
    lowercase: true,
    trim: true
  },
  description: {
    type: String,
    required: [true, 'Product description is required'],
    trim: true,
    maxlength: [2000, 'Product description cannot exceed 2000 characters']
  },
  shortDescription: {
    type: String,
    trim: true,
    maxlength: [500, 'Short description cannot exceed 500 characters']
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: false
  },
  subcategory: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category'
  },
  brand: {
    type: String,
    trim: true,
    maxlength: [100, 'Brand name cannot exceed 100 characters']
  },
  sku: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true
  },
  barcode: {
    type: String,
    trim: true,
    sparse: true
  },
  images: [imageSchema],
  pricing: pricingSchema,
  inventory: inventorySchema,
  variants: [variantSchema],
  attributes: [attributeSchema],
  colors: [colorSchema],
  sizes: [sizeSchema],

  highlights: [{
    type: String,
    trim: true,
    maxlength: [200, 'Highlight cannot exceed 200 characters']
  }],
  deliveryCharges: deliveryChargesSchema,
  shippingOptions: shippingOptionsSchema,
  returnPolicy: returnPolicySchema,
  warranty: warrantySchema,
  specifications: specificationsSchema,
  shipping: shippingSchema,
  seo: seoSchema,
  status: {
    type: String,
    enum: ['draft', 'pending_approval', 'active', 'inactive', 'archived', 'rejected'],
    default: 'draft'
  },
  approval: approvalSchema,
  visibility: {
    type: String,
    enum: ['public', 'private', 'password_protected'],
    default: 'public'
  },
  featured: {
    type: Boolean,
    default: false
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  reviews: reviewsSchema,
  sales: salesSchema,
  analytics: analyticsSchema,
  publishedAt: Date,
  lastModified: {
    type: Date,
    default: Date.now
  },
  modifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Add indexes from modular component
addProductIndexes(productSchema);

// Add virtuals from modular component
addProductVirtuals(productSchema);

// Add middleware from modular component
addProductMiddleware(productSchema);

// Add static methods from modular component
addProductStatics(productSchema);

// Add instance methods from modular component
addProductMethods(productSchema);

// Add currency methods from modular component
addProductCurrencyMethods(productSchema);

module.exports = mongoose.model('Product', productSchema);
