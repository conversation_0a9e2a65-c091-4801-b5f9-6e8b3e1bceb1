// Debug script to identify problematic routes
const express = require('express');
const fs = require('fs');
const path = require('path');

console.log('🔍 Debugging route registration...\n');

// Override express router to catch problematic routes
const originalRouter = express.Router;
express.Router = function(...args) {
  const router = originalRouter.apply(this, args);
  
  // Override route methods to catch errors
  ['get', 'post', 'put', 'patch', 'delete'].forEach(method => {
    const originalMethod = router[method];
    router[method] = function(path, ...handlers) {
      try {
        console.log(`✅ Registering ${method.toUpperCase()} ${path}`);
        return originalMethod.call(this, path, ...handlers);
      } catch (error) {
        console.error(`❌ Error registering ${method.toUpperCase()} ${path}:`, error.message);
        throw error;
      }
    };
  });
  
  return router;
};

try {
  console.log('Loading app...');
  const app = require('./src/app');
  console.log('✅ App loaded successfully!');
} catch (error) {
  console.error('❌ Error loading app:', error.message);
  console.error('Stack trace:', error.stack);
}
