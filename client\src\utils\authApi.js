// Authentication API utilities
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

class AuthAPI {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        ...options.headers,
      },
      ...options,
    };

    // Set Content-Type based on body type
    if (options.body instanceof FormData) {
      // Let browser set Content-Type with boundary for FormData
      // Don't set Content-Type header
    } else if (options.body && typeof options.body === 'string') {
      // For JSON strings, explicitly set application/json
      config.headers['Content-Type'] = 'application/json';
    }

    try {
      const response = await fetch(url, config);
      
      // Check if response is JSON
      const contentType = response.headers.get('content-type');
      let data;
      
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        const text = await response.text();
        data = { message: text || 'Server error' };
      }

      if (!response.ok) {
        const error = new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
        error.response = { data, status: response.status, statusText: response.statusText };
        throw error;
      }

      return data;
    } catch (error) {
      // Handle network connection errors
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        const networkError = new Error('Unable to connect to server. Please check your internet connection and try again.');
        networkError.isNetworkError = true;
        throw networkError;
      }
      
      console.error('API Request failed:', error);
      throw error;
    }
  }

  // Unified Authentication (handles both user and vendor)
  async login(credentials) {
    return this.makeRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async register(userData) {
    return this.makeRequest('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  // Legacy methods for backward compatibility
  async loginUser(credentials) {
    return this.login({ ...credentials, userType: 'customer' });
  }

  async registerUser(userData) {
    return this.register({ ...userData, userType: 'customer' });
  }

  async loginVendor(credentials) {
    return this.login({ ...credentials, userType: 'vendor' });
  }

  async registerVendor(vendorData) {
    return this.register({ ...vendorData, userType: 'vendor' });
  }

  // Password Reset
  async forgotPassword(email) {
    return this.makeRequest('/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  async resetPassword(resetData) {
    return this.makeRequest('/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify(resetData),
    });
  }

  // Email Verification
  async verifyEmail(token, email) {
    return this.makeRequest(`/auth/verify-email?token=${token}&email=${email}`, {
      method: 'GET',
    });
  }

  async resendVerificationEmail(email) {
    return this.makeRequest('/auth/resend-verification', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  // Token Management
  async refreshToken(refreshToken) {
    return this.makeRequest('/auth/refresh-token', {
      method: 'POST',
      body: JSON.stringify({ refreshToken }),
    });
  }

  async logout(token) {
    return this.makeRequest('/auth/logout', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  }

  // Helper method to determine the correct profile endpoint based on user type
  getProfileEndpoint(userType = null) {
    // If userType is not provided, try to get it from localStorage
    if (!userType) {
      const authUser = localStorage.getItem('authUser');
      if (authUser) {
        try {
          const user = JSON.parse(authUser);
          userType = user.userType;
        } catch (e) {
          console.warn('Failed to parse authUser from localStorage:', e);
        }
      }
      
      // Fallback to userType stored separately
      if (!userType) {
        userType = localStorage.getItem('authUserType') || 'customer';
      }
    }
    
    // Return appropriate endpoint based on user type
    switch (userType) {
      case 'admin':
        return '/admin/profile';
      case 'vendor':
        return '/vendor/profile';
      case 'customer':
      default:
        return '/customer/profile';
    }
  }

  // Profile Management
  async getProfile(token) {
    return this.makeRequest('/auth/profile', {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  }

  async updateProfile(token, profileData, userType = null) {
    console.log('🔍 AuthAPI updateProfile called with:', {
      token: token ? 'EXISTS' : 'MISSING',
      profileData,
      userType,
      isFormData: profileData instanceof FormData,
      dataType: typeof profileData,
      dataKeys: profileData ? Object.keys(profileData) : null
    });
    
    // Get the correct profile endpoint based on user type
    const profileEndpoint = this.getProfileEndpoint(userType);
    console.log('📍 Using profile endpoint:', profileEndpoint);
    
    // Check if profileData is FormData (for file uploads) or regular object
    if (profileData instanceof FormData) {
      console.log('📤 Sending FormData request to profile endpoint:', profileEndpoint);
      return this.makeRequest(profileEndpoint, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: profileData,
      });
    } else {
      console.log('📤 Sending JSON request to profile endpoint:', profileEndpoint, 'with body:', JSON.stringify(profileData));
      return this.makeRequest(profileEndpoint, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(profileData),
      });
    }
  }

  async updateProfilePicture(token, imageFile, userType = null) {
    const formData = new FormData();
    formData.append('avatar', imageFile);

    const profileEndpoint = this.getProfileEndpoint(userType);
    const avatarEndpoint = `${profileEndpoint}/avatar`;

    return this.makeRequest(avatarEndpoint, {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });
  }

  async deleteProfilePicture(token, userType = null) {
    const profileEndpoint = this.getProfileEndpoint(userType);
    const avatarEndpoint = `${profileEndpoint}/avatar`;

    return this.makeRequest(avatarEndpoint, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  }

  // Single address system for simplified checkout
  async updateAddress(token, addressData) {
    return this.makeRequest('/auth/profile', {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(addressData),
    });
  }

  // Legacy multi-address methods (keeping for backward compatibility)
  async addAddress(token, addressData) {
    return this.makeRequest('/customer/profile/addresses', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(addressData),
    });
  }

  async updateAddressById(token, addressId, addressData) {
    return this.makeRequest(`/customer/profile/addresses/${addressId}`, {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(addressData),
    });
  }

  async deleteAddress(token, addressId) {
    return this.makeRequest(`/customer/profile/addresses/${addressId}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  }

  async updatePreferences(token, preferences) {
    return this.makeRequest('/customer/profile/preferences', {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ preferences }),
    });
  }

  async changePassword(token, passwordData) {
    return this.makeRequest('/auth/change-password', {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(passwordData),
    });
  }

  // Health Check
  async healthCheck() {
    return this.makeRequest('/auth/health', {
      method: 'GET',
    });
  }
}

// Create and export a singleton instance
const authAPI = new AuthAPI();
export { authAPI };

// Export individual methods for convenience
export const {
  login,
  register,
  loginUser,
  registerUser,
  loginVendor,
  registerVendor,
  forgotPassword,
  resetPassword,
  verifyEmail,
  resendVerificationEmail,
  refreshToken,
  logout,
  getProfile,
  updateProfile,
  updateProfilePicture,
  deleteProfilePicture,
  addAddress,
  updateAddress,
  deleteAddress,
  updatePreferences,
  changePassword,
  healthCheck,
} = authAPI;

export default authAPI;