import React from 'react'

const SignInHoverModal = ({ showModal, onMouseEnter, onMouseLeave }) => {
    if (!showModal) return null

    return (
        <div 
            className="absolute top-full right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-xl z-50"
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
        >
            <div className="p-6">
                {/* Header */}
                <div className="text-center mb-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Welcome to Alicartify.com!</h3>
                </div>
                
                {/* Sign in button */}
                <button className="w-full bg-orange-500 hover:bg-orange-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200">
                    Sign in
                </button>
            </div>
            
            {/* Triangle pointer */}
            <div className="absolute -top-2 right-4 w-4 h-4 bg-white border-l border-t border-gray-200 transform rotate-45"></div>
        </div>
    )
}

export default SignInHoverModal
