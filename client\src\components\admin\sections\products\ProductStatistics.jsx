import React, { useEffect } from 'react';
import { Row, Col, Card, Statistic } from 'antd';
import {
  ShoppingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';

const ProductStatistics = ({ 
  stats, 
  pendingCount, 
  loading,
  allProducts = [] 
}) => {
  // Calculate dynamic statistics from actual data as fallback
  const calculateDynamicStats = () => {
    const totalProducts = allProducts.length;
    
    // Check for different possible status values
    const activeProducts = allProducts.filter(product => {
      const status = product.status?.toLowerCase();
      return status === 'active' || status === 'approved' || product.active === true;
    }).length;
    
    const lowStockProducts = allProducts.filter(product => {
      // Check multiple possible inventory structures
      const inventory = product.inventory || product.stock || {};
      const quantity = inventory.quantity || product.quantity || 0;
      const threshold = inventory.lowStockThreshold || inventory.threshold || 5;
      
      // Consider low stock if quantity <= threshold
      if (inventory.trackQuantity !== false) {
        return quantity <= threshold;
      }
      return false;
    }).length;

    return {
      totalProducts,
      activeProducts,
      lowStockProducts
    };
  };

  const dynamicStats = calculateDynamicStats();
  
  // Debug logging to understand data structure
  useEffect(() => {
    console.log('🔍 ProductStatistics Debug:', {
      stats,
      pendingCount,
      allProductsLength: allProducts.length,
      dynamicStats,
      sampleProduct: allProducts[0],
      statsKeys: stats ? Object.keys(stats) : 'no stats'
    });
  }, [stats, pendingCount, allProducts, dynamicStats]);

  // Get the final values to display
  const totalProducts = stats.totalProducts ?? dynamicStats.totalProducts ?? 0;
  const activeProducts = stats.activeProducts ?? dynamicStats.activeProducts ?? 0;
  const lowStockProducts = stats.lowStockProducts ?? dynamicStats.lowStockProducts ?? 0;
  const pendingProducts = pendingCount ?? 0;

  return (
    <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="Total Products"
            value={totalProducts}
            prefix={<ShoppingOutlined />}
            valueStyle={{ color: '#1890ff' }}
            loading={loading}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="Pending Approval"
            value={pendingProducts}
            prefix={<ClockCircleOutlined />}
            valueStyle={{ color: '#faad14' }}
            loading={loading}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="Active Products"
            value={activeProducts}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#52c41a' }}
            loading={loading}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="Low Stock"
            value={lowStockProducts}
            prefix={<ExclamationCircleOutlined />}
            valueStyle={{ color: '#ff4d4f' }}
            loading={loading}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default ProductStatistics;
