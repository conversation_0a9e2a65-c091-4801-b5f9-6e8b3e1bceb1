const express = require('express');
const router = express.Router();
const reviewController = require('../controllers/reviewController');
const { verifyToken } = require('../middleware/authMiddleware');
const { body, param, query } = require('express-validator');
const { validateRequest } = require('../middleware/validation');

// Validation middleware
const reviewValidation = [
  body('productId')
    .isMongoId()
    .withMessage('Invalid product ID'),
  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('comment')
    .trim()
    .isLength({ min: 10, max: 300 })
    .withMessage('Comment must be between 10 and 300 characters'),
  validateRequest
];

const replyValidation = [
  body('message')
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Reply message must be between 10 and 500 characters'),
  validateRequest
];

const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
  validateRequest
];

// Public routes (no authentication required)
// Get reviews for a specific product
router.get('/product/:productId', 
  param('productId').isMongoId().withMessage('Invalid product ID'),
  query('sortBy').optional().isIn(['newest', 'oldest', 'highest-rating', 'lowest-rating']),
  paginationValidation,
  reviewController.getProductReviews
);

// Protected routes (authentication required)
// Check if customer can review a product
router.get('/can-review/:productId',
  verifyToken,
  param('productId').isMongoId().withMessage('Invalid product ID'),
  validateRequest,
  reviewController.canReviewProduct
);

// Create a new review (customers only)
router.post('/',
  verifyToken,
  reviewValidation,
  reviewController.createReview
);

// Get customer's own reviews
router.get('/my-reviews',
  verifyToken,
  paginationValidation,
  reviewController.getCustomerReviews
);

// Vendor routes
// Get reviews for vendor's products
router.get('/vendor/my-reviews',
  verifyToken,
  paginationValidation,
  reviewController.getVendorReviews
);

// Reply to a review (vendors only)
router.post('/:reviewId/reply',
  verifyToken,
  param('reviewId').isMongoId().withMessage('Invalid review ID'),
  replyValidation,
  reviewController.replyToReview
);

// Update a reply (vendors only)
router.put('/reply/:replyId',
  verifyToken,
  param('replyId').isMongoId().withMessage('Invalid reply ID'),
  replyValidation,
  reviewController.updateReply
);

// Delete a reply (vendors only)
router.delete('/reply/:replyId',
  verifyToken,
  param('replyId').isMongoId().withMessage('Invalid reply ID'),
  validateRequest,
  reviewController.deleteReply
);

module.exports = router;
